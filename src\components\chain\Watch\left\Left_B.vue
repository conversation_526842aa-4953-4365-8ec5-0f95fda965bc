<!-- 左二 -->
<template>
  <div class="flex flex_col flex_J_SB por list_item h32">
    <p class="NB_foot_4 item_title">两化融合发展水平</p>
    <div style="width: 100%; height: 85%" ref="Left_B"></div>
  </div>
</template>

<script setup name="Left_B">
import * as echarts from "echarts";
import { ref, onMounted } from "vue";
let Left_B = ref(null);
var myChart_A;
onMounted(() => {
  getData();
});
function resizeHandler_A() {
  myChart_A.resize();
}
async function getData() {
  var currentIndex = 0;
  window.removeEventListener("resize", resizeHandler_A);
  window.addEventListener("resize", resizeHandler_A);
  let intervalId;
  clearInterval(intervalId);
  if (myChart_A != null && myChart_A != "" && myChart_A != undefined)
    myChart_A.dispose();
  myChart_A = echarts.init(Left_B.value);

  var iconData = [];
  // 绘制左侧面
  const CubeLeft = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0,
    },
    buildPath: function (ctx, shape) {
      // 会canvas的应该都能看得懂，shape是从custom传入的
      const xAxisPoint = shape.xAxisPoint;
      // console.log(shape)
      const c0 = [shape.x + 8, shape.y];
      const c1 = [shape.x - 6, shape.y];
      const c2 = [xAxisPoint[0] - 6, xAxisPoint[1]];
      const c3 = [xAxisPoint[0] + 8, xAxisPoint[1]];
      ctx
        .moveTo(c0[0], c0[1])
        .lineTo(c1[0], c1[1])
        .lineTo(c2[0], c2[1])
        .lineTo(c3[0], c3[1])
        .closePath();
    },
  });
  // 绘制右侧面
  const CubeRight = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0,
    },
    buildPath: function (ctx, shape) {
      // console.log(shape)
      const xAxisPoint = shape.xAxisPoint;
      // console.log(xAxisPoint)
      const c1 = [shape.x + 8, shape.y];
      const c2 = [xAxisPoint[0] + 8, xAxisPoint[1]];
      const c3 = [xAxisPoint[0] + 16, xAxisPoint[1] - 0];
      const c4 = [shape.x + 16, shape.y - 8];
      ctx
        .moveTo(c1[0], c1[1])
        .lineTo(c2[0], c2[1])
        .lineTo(c3[0], c3[1])
        .lineTo(c4[0], c4[1])
        .closePath();
    },
  });
  // 绘制顶面
  const CubeTop = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0,
    },
    buildPath: function (ctx, shape) {
      const c1 = [shape.x + 8, shape.y];
      const c2 = [shape.x + 16, shape.y - 8]; //右点
      const c3 = [shape.x - -3, shape.y - 8];
      const c4 = [shape.x - 6, shape.y];
      ctx
        .moveTo(c1[0], c1[1])
        .lineTo(c2[0], c2[1])
        .lineTo(c3[0], c3[1])
        .lineTo(c4[0], c4[1])
        .closePath();
    },
  });

  // 注册三个面图形
  echarts.graphic.registerShape("CubeLeft", CubeLeft);
  echarts.graphic.registerShape("CubeRight", CubeRight);
  echarts.graphic.registerShape("CubeTop", CubeTop);

  var option = {
    grid: {
      left: "0%",
      top: "20%",
      bottom: "14%",
      right: "0%",
      containLabel: false,
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        textStyle: {
          color: "#fff",
        },
      },
      formatter: function (params) {
        var date = params[0].axisValue;
        var tooltipContent =
          date + "<br/>" + params[0].marker + " 企业数量: " + params[0].value;
        return tooltipContent;
      },
    },
    xAxis: {
      type: "category",
      data: ["L4及以下", "L5", "L6", "L7"],
      axisLine: {
        show: true,
        lineStyle: {
          type: "dashed",
          color: "rgba(244, 244, 244,0.1)",
        },
      },
      axisTick: {
        show: false,
      },
      offset: 4,
      axisLabel: {
        interval: 0,
        textStyle: {
          color: "white",
        },
        align: "center", // 添加这一行来重新居中对齐标签
      },
    },
    yAxis: {
      type: "value",
      splitLine: {
        show: true,
        lineStyle: { type: "dashed", color: "rgba(77, 128, 254, 0.2)" },
      },
      offset: 10,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        fontSize: 14,
        interval: 0,
        textStyle: {
          color: "white",
        },
      },
    },
    series: [
      {
        type: "custom",
        name: "交易数",
        renderItem: (params, api) => {
          const location = api.coord([api.value(0), api.value(1)]);
          return {
            type: "group",
            children: [
              {
                type: "CubeLeft",
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#0078e4",
                    },
                    {
                      offset: 1,
                      color: "#1b83fe",
                    },
                  ]),
                },
              },
              {
                type: "CubeRight",
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#04dec7",
                    },
                    {
                      offset: 1,
                      color: "#003ff9",
                    },
                  ]),
                },
              },
              {
                type: "CubeTop",
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0]),
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#1b83fe",
                    },
                    {
                      offset: 1,
                      color: "#27f7e1",
                    },
                  ]),
                },
              },
            ],
          };
        },
        // data: stores.right_three,
        data: [1024, 410, 300, 70],
      },

      {
        type: "pictorialBar",
        label: {
          normal: {
            show: true,
            position: "top",
            distance: -50,
            formatter: () => {
              return " ";
            },
          },
        },
        tooltip: {
          trigger: false, // 确保 tooltip 只在 item 上显示
        },
        z: 2,
        symbolPosition: "end",
        symbolSize: [35, 20],
        symbolOffset: [5, "-22"],
        itemStyle: {
          shadowColor: "rgba(0,0,0,.3)",
          shadowBlur: 10,
          shadowOffsetY: 1,
          shadowOffsetX: 1,
        },
        data: iconData,
      },
    ],
  };
  myChart_A.setOption(option);

  intervalId = setInterval(() => {
    var dataLen = option.series[0].data.length;

    currentIndex = (currentIndex + 1) % dataLen;

    option.series.forEach((series, index) => {
      myChart_A.dispatchAction({
        type: "highlight",
        seriesIndex: index,
        dataIndex: currentIndex,
      });

      // Show tooltip for each series
      myChart_A.dispatchAction({
        type: "showTip",
        seriesIndex: index,
        dataIndex: currentIndex,
      });
    });
  }, 4000);
}
</script>
