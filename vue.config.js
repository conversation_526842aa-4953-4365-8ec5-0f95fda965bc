const { defineConfig } = require("@vue/cli-service");
const compressionWebpackPlugin = require("compression-webpack-plugin");

module.exports = defineConfig({
  transpileDependencies: true,
  publicPath: "./",
  devServer: {
    open: true,
    host: "localhost",
    port: 1234,
    // proxy: {
    //   // 海汇
    //   "/api": {
    //     // target: "http://************:8281/",// 本地
    //     target: "https://mdf.ningmengdou.com/api/blade-screen/haihui/screen/bank/",// 线上
    //     ws: true,
    //     changOrigin: true,
    //     pathRewrite: { "^/api": "" },
    //   },
    // },
  },
  productionSourceMap: false,
  filenameHashing: false,
  chainWebpack: (config) => {
    // if (!_IS_DEV__) {
    config.plugin("compressionPlugin").use(
      new compressionWebpackPlugin({
        algorithm: "gzip",
        test: /\.js$|\.html$/,
        threshold: 10240,
        minRatio: 0.6, //
        deleteOriginalAssets: false,
      })
    );
    // }
  },
});
