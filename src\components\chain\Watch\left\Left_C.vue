<!-- 左三 -->
<template>
  <div class="flex flex_col flex_J_SB por list_item h32">
    <p class="NB_foot_4 item_title">产业数字化发展水平排名</p>
    <div class="table_head flex flex_J_SB">
      <div
        class="flex flex_J_C flex_A_C table_headA fs14- font_noWarp text_noWrapA"
      >
        排名
      </div>
      <div class="flex flex_J_C flex_A_C fs14 table_headB tc por head2">
        区域名称
      </div>
      <div class="flex flex_J_C flex_A_C fs14 table_headC tc text_l">
        产业数字化发展水平指数
      </div>
    </div>
    <div class="table_body flex1 over">
      <vue3-seamless-scroll
        direction="up"
        :hover="true"
        :isWatch="true"
        :limitScrollNum="right_one_list.length"
        :singleLine="true"
        :list="right_one_list"
        :step="0.4"
        class="DongTable flex flex_col"
      >
        <ul class="flex flex_col flex_J_SB">
          <li
            v-for="(item, i) in right_one_list"
            :key="i"
            class="flex flex_J_SB item_row tr"
            :class="[i % 2 != 1 ? 'row_j' : 'row_t']"
          >
            <div class="flex flex_J_C flex_A_C table_headA">
              <span class="w100 tc fs14- text_noWrapA">
                <!-- {{ item.name || "暂未填写" }} -->
                {{ i + 1 }}
              </span>
            </div>
            <div class="flex flex_A_C fs14- table_headB body_b">
              <span class="w100 tc text_noWrapA">
                {{ item.location || "暂未填写" }}
              </span>
            </div>
            <div class="flex flex_J_C flex_A_C fs14 table_headC body_b">
              {{ item.tag || "暂未填写" }}
            </div>
          </li>
        </ul>
      </vue3-seamless-scroll>
    </div>
  </div>
</template>

<script setup name="Left_C">
import { ref, onMounted } from "vue";

let right_one_list = ref([
  {
    location: "吉安县",
    tag: "106.2",
  },
  {
    location: "吉州区",
    tag: "91.4",
  },
  {
    location: "万安县",
    tag: "89.5",
  },
  {
    location: "遂川县",
    tag: "86.1",
  },
  {
    location: "井冈山市",
    tag: "77.6",
  },
  {
    location: "吉安市",
    tag: "77.3",
  },
  {
    location: "峡江县",
    tag: "73.9",
  },
  {
    location: "安福县",
    tag: "73.6",
  },
  {
    location: "泰和县",
    tag: "72.7",
  },
  {
    location: "永新县",
    tag: "71.6",
  },
  {
    location: "吉水县",
    tag: "69.7",
  },
  {
    location: "青原区",
    tag: "68.4",
  },
  {
    location: "永丰县",
    tag: "62.9",
  },
  {
    location: "新干县",
    tag: "60.8",
  },
]);
onMounted(() => {});
</script>

<style scoped lang="scss">
.table_head {
  background-color: #015bff;
  height: 34px;
  min-height: 34px;
  margin-top: 0.8vmin;
}
.head1 {
  padding-left: 10%;
  &::before {
    content: "";
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1.2px;
    height: 60%;
    background-color: #fff;
  }
}

.table_headA {
  width: 15%;
  text-align: left;
}
.table_headC {
  width: 44%;
}
.table_headB {
  width: 20%;
}
.table_body {
  .item_row {
    height: 34px;
    &:hover {
      background-color: rgba(135, 206, 235, 0.2);
    }
  }
  ::v-deep .DongTable {
    & > div {
      display: flex;
      flex-direction: column;
      will-change: transform;
    }
  }
  .row_t {
    color: #718bbd;
  }
  .row_j {
    background: rgba(1, 91, 255, 0.2);
  }
}
</style>
