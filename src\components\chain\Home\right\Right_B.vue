<!-- 左二 -->
<template>
  <div class="flex flex_col flex_J_SB por list_item h32">
    <p class="NB_foot_4 item_title">注册资本</p>
    <div style="width: 100%; height: 85%" ref="Left_B"></div>
  </div>
</template>

<script setup name="Left_B">
import * as echarts from "echarts";
import { ref, onMounted } from "vue";
import useCounter from "@/stores/chain/chain.js";
let Left_B = ref(null);
var myChart;
onMounted(() => {
  getData();
  // list.value = stores.need_data;
});
function resizeHandler() {
  myChart.resize();
}
async function getData() {
  window.removeEventListener("resize", resizeHandler);
  window.addEventListener("resize", resizeHandler);
  myChart = echarts.init(Left_B.value);
  let data1 = [
    "100万以下",
    "100-500",
    "500-1000",
    "1000-5000",
    "5000-1亿",
    "1亿以上",
  ];
  let data2 = [
    {
      value: 13,
      name: "100万以下",
    },
    {
      value: 50.62,
      name: "100-500",
    },
    {
      value: 4.57,
      name: "500-1000",
    },
    {
      value: 14.81,
      name: "1000-5000",
    },
    {
      value: 7,
      name: "5000-1亿",
    },
    {
      value: 10,
      name: "1亿以上",
    },
  ];
  var colorList = [
    {
      c1: " #7DEBFF",
      c2: "#3BB3FF",
    },
    {
      c1: "#5FE48E",
      c2: "#37C76A",
    },
    {
      c1: "#9085FF",
      c2: "#503EFF",
    },
    {
      c1: "#F9D172",
      c2: "#FFBB18",
    },
    {
      c1: " #85C9FF",
      c2: "#8AC2F9",
    },
    {
      // 新增的颜色组，用于第六个数据项
      c1: "#FF6A6A", // 示例颜色，您可以根据喜好调整
      c2: "#FF0000",
    },
  ];
  let option = {
    tooltip: {
      trigger: "item",
      formatter: "销量 <br/>{b} : ({d}%)",
    },
    legend: {
      formatter: function (name) {
        var data = option.series[0].data;
        var total = 0;
        var tarValue;
        for (var i = 0; i < data.length; i++) {
          total += data[i].value;
          if (data[i].name == name) {
            tarValue = data[i].value;
          }
        }
        var p = Math.round((tarValue / total) * 1000) / 10;
        return `${name} ${p}%`; // 移除多余的空格，使文本更紧凑
      },
      orient: "vertical",
      right: "0%", // 移动到右侧，并留出一些边距
      top: "center", // 垂直居中
      itemWidth: 15,
      itemHeight: 15,
      data: data1,
      textStyle: {
        color: "#fff",
        // rich:{
        //     a:
        // }
      },
      itemGap: 10, // 减小图例项之间的间隔
      icon: "circle",
    },
    series: [
      {
        name: "访问来源",
        type: "pie",
        radius: "65%",
        center: ["40%", "50%"],
        data: data2,
        roseType: "radius",
        startAngle: 90,
        // roseType:false,
        label: {
          show: true,
          // formatter: "{c} 件",
          // show: true,
          // position: "top",
          textStyle: {
            color: "#fff",
          },
        },
        itemStyle: {
          normal: {
            color: function (params) {
              return new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                {
                  offset: 0,
                  color: colorList[params.dataIndex].c1,
                },
                {
                  offset: 1,
                  color: colorList[params.dataIndex].c2,
                },
              ]);
            },
          },
        },
      },
    ],
  };
  myChart.setOption(option, true);
}
</script>
