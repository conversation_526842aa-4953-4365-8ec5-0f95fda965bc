<!-- 右上 -->
<template>
  <div class="flex flex_col flex_J_SB por list_item h32">
    <p class="NB_foot_4 item_title">融资总额</p>

    <div style="width: 100%; height: 86%" ref="Right_A"></div>
  </div>
</template>

<script setup name="Right_A">
import { ref, onMounted } from "vue";
import * as echarts from "echarts";
onMounted(() => {
  getData();
});
let Right_A = ref(null);
let currentIndex = 0;
var myChart;
let intervalId;
async function getData() {
  window.removeEventListener("resize", resizeHandler);
  window.addEventListener("resize", resizeHandler);

  clearInterval(intervalId);
  if (myChart != null && myChart != "" && myChart != undefined)
    myChart.dispose();
  myChart = echarts.init(Right_A.value);

  let X_data = [
    ["2022年", "8月", "2"],
    ["2023年", "9月", "5"],
    ["2024年", "6月", "10"],
  ];

  const option = {
    animation: true,
    grid: {
      left: "0%",
      top: "14%",
      bottom: "0%",
      right: "6%",
      containLabel: true,
    },
    xAxis: {
      data: X_data.map((item, i) => item[0]),
      axisLine: {
        show: true,
        lineStyle: {
          color: "rgba(72, 208, 181, 0.6)",
        },
      },
      axisTick: {
        show: false, //隐藏X轴轴线
      },
      boundaryGap: false,
      axisLabel: {
        show: true,
        // margin: 14,
        fontSize: 14,
        textStyle: {
          color: "#fff", //X轴文字颜色
        },
      },
    },
    yAxis: [
      {
        type: "value",
        min: 1,
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        splitLine: {
          show: true,
          lineStyle: { type: "dashed", color: "rgba(77, 128, 254, 0.2)" },
        },
        axisLabel: {
          show: true,
          margin: 24,
          fontSize: 12,
          textStyle: {
            color: "#fff",
            fontSize: 12,
            fontFamily: "Microsoft YaHei",
          },
          // 添加 formatter 属性，将单位“亿”添加到标签
          formatter: "{value}亿",
        },
      },
    ],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        // type: "shadow",
        crossStyle: {
          color: "#999",
        },
      },
      showDelay: 0,
      hideDelay: 0,
      enterable: true, //鼠标是否可进入提示框浮层中，默认为false，如需详情内交互，如添加链接，按钮，可设置为 true
      transitionDuration: 0.5, //提示框浮层的移动动画过渡时间，单位是 s，设置为 0 的时候会紧跟着鼠标移动
      formatter: function (params) {
        var res = params.map(function (item, i) {
          let newName = item.name.replace("\n", "<br>");
          return (
            item.data.time +
            // " - " +
            // newName +
            "<br/>" +
            item.marker +
            " " +
            item.value +
            "亿"
          );
        });
        return res.join("<br/>");
      },
    },
    series: [
      {
        name: "入驻企业",
        type: "line",
        showAllSymbol: true,
        symbolSize: 6,
        lineStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "#2cb2ff",
              },
              {
                offset: 1,
                color: "#2cb2ff",
              },
            ]),
          },
        },
        itemStyle: {
          color: "#2cb2ff",
        },
        symbol: "emptyCircle", // 小点点形状设置为空心圆
        label: {
          formatter: "{c}亿",
          show: true,
          position: "top",
          textStyle: {
            color: "#fff",
          },
        },
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: "rgba(3, 54, 255,0.9)",
                },
                {
                  offset: 1,
                  color: "rgba(1, 180, 255,0.6)",
                },
              ],
              false
            ),
          },
        },
        data: X_data.map((item, i) => {
          return {
            value: item[2],
            time: item[0],
          };
        }),
        // data: Left_data.Y1,
      },
    ],
  };

  myChart.setOption(option);
  currentIndex = 0;

  intervalId = setInterval(() => {
    var dataLen = option.series[0].data.length;
    currentIndex = (currentIndex + 1) % dataLen;
    option.series.forEach((series, index) => {
      myChart.dispatchAction({
        type: "highlight",
        seriesIndex: index,
        dataIndex: currentIndex,
      });

      // Show tooltip for each series
      myChart.dispatchAction({
        type: "showTip",
        seriesIndex: index,
        dataIndex: currentIndex,
      });
    });
  }, 4000);
}
function resizeHandler() {
  myChart.resize();
}
</script>
