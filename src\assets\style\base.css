/* 样式初始化 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
html,
body {
  width: 100%;
  height: 100%;
}
a {
  text-decoration: none;
}
body,
p,
h1,
h2,
h3,
h4,
h5,
h6,
ul,
ol,
dl,
dt,
dd,
li {
  margin: 0;
  padding: 0;
  list-style: none;
  color: #fff;
  font-family: "微软雅黑";
  font-size: initial;
}

img,
input {
  vertical-align: middle;
  border: 0 none;
  outline-style: none;
  padding: 0;
}

/* 鼠标移入变成小手 */
.cursor {
  cursor: pointer;
}
.wc {
  will-change: transform;
}
.tr {
  transition: all 0.3s ease;
}
/* 单行文本溢出隐藏 */
.text_noWrap {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.text_noWrapA {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.font_noWarp {
  white-space: nowrap;
}
.font_skew {
  transform: skew(-6deg);
}
.fw9 {
  font-weight: 900;
}
.NB_foot_1 {
  font-family: "Excellent Title Black";
  color: transparent;
  background: linear-gradient(180deg, #e7913e 0%, #f6c3aa 100%);
  background-clip: text;
  font-weight: 900;
  transform: skew(-6deg);
}
.NB_foot_2 {
  font-family: "Excellent Title Black";
  color: transparent;
  background: linear-gradient(180deg, #ffffff 0%, #96f4ff 100%);
  background-clip: text;
  font-weight: 900;
  transform: skew(-6deg);
}
.NB_foot_3 {
  font-family: "Excellent Title Black";
  color: transparent;
  background: linear-gradient(180deg, #ffffff 0%, #86a6fc 100%);
  background-clip: text;
  font-weight: 900;
  transform: skew(-6deg);
}
.NB_foot_4 {
  font-weight: 900;
  font-family: "Excellent Title Black";
  color: transparent;
  background: linear-gradient(180deg, #306df8 10%, #94d1ff 15%, #279cff 100%);
  background-clip: text;
  transform: skew(-6deg);
}
.NB_foot_5 {
  font-family: "Excellent Title Black";
  color: transparent;
  background-image: linear-gradient(100deg, #02e3f1, #a3ffff, #02e3f1);
  background-clip: text;
  font-weight: 900;
  transform: skew(-6deg);
}
.NB_foot_6 {
  transform: skew(-6deg);
  font-family: "Excellent Title Black";
  color: transparent !important;
  background: radial-gradient(circle, #96f4ff 0%, #2ee4e9);
  background-clip: text;
  font-weight: 900;
}
.NB_foot_8 {
  transform: skew(-6deg);
  font-family: "Excellent Title Black";
  color: transparent;
  background: linear-gradient(180deg, #ffffff 0%, #2abcf9 100%);
  background-clip: text;
  font-weight: 900;
  font-size: 2.14vmin;
}
.NB_foot_7 {
  font-family: "Excellent Title Black";
  color: transparent;
  background: linear-gradient(180deg, #ffffff 0%, #367cfb 100%);
  background-clip: text;
  font-weight: 900;
  transform: skew(-6deg);
  font-size: 2.14vmin;
}

/* 滚动数字 */
.NB_foot_9 {
  transform: skew(-6deg);
  font-family: "Excellent Title Black";
  color: transparent !important;
  background: radial-gradient(circle, #96f4ff 0%, #96aed1);
  background-clip: text;
  font-weight: 900;
}
.opacity0 {
  opacity: 0;
}
.opacity1 {
  opacity: 1;
}
.fc_1 {
  color: #01faff;
}
.fc_2 {
  color: #36fee9;
}
.fc_3 {
  color: #367cfb;
}
.fc_4 {
  color: #04e2c7;
}
.fc_5 {
  color: #95b0e7;
}
.fc_6 {
  color: #fff;
}
.fc_7 {
  color: #fbecb1;
}
.fc_8 {
  color: #01beff;
}
.fc_9 {
  color: #5ae5ee;
}
.fc_10 {
  color: #e3eef8;
}
.fc_11 {
  color: #cec17d;
}
.fc_12 {
  color: #f78989;
}
.fc_13 {
  color: #409eff;
}
.fc_14 {
  color: #67c23a;
}
.fc_15 {
  color: #eedb66;
}
.fc_16 {
  color: #f9f23e;
}
.fc_17 {
  color: #00faa8;
}
.fc_18 {
  color: #f93e3e;
}
.fc_19 {
  color: #1ae5a9;
}
.fc_20 {
  color: #66c3fe;
}
.fc_21 {
  color: #37ed88;
}
.fc_22 {
  color: #d21639;
}
.fc_24 {
  color: #03fcfe;
}
.fc_25 {
  color: #5daafd;
}
.fc_26 {
  color: #0ef9b3;
}
.fc_27 {
  color: #4bb3eb;
}
.fc_28 {
  color: #40ffd8;
}
.fc_29 {
  color: #65dcff;
}
.fc_30 {
  color: #a6a7be;
}
.fc_31 {
  color: #fbcf89;
}
.fc_32 {
  color: #123cac;
}
.fs12 {
  font-size: 1.2vmin;
}
.fs14 {
  font-size: 1.475vmin !important;
}
.fs14- {
  font-size: 1.3vmin;
}
.fs16 {
  font-size: 1.66vmin !important;
}
.fs18 {
  font-size: 1.92vmin;
}
.fs20 {
  font-size: 2.2vmin !important;
}
.fs22 {
  font-size: 2.45vmin !important;
}
.fs24 {
  font-size: 2.76vmin !important;
}
.tc {
  text-align: center;
}
.ter {
  text-align: right;
}
.tel {
  text-align: left;
}
.flex {
  display: flex;
}
.flex_warp {
  flex-wrap: wrap;
}
.flex_col {
  flex-direction: column; /* 独行 下 */
}
.flexs_t {
  align-content: flex-start; /* 多行向上 */
}
.flexs_SB {
  align-content: space-between;
}
.flexs_SA {
  align-content: space-around;
}
.flex_A_C {
  align-items: center;
}
.flex_A_B {
  align-items: flex-end; /* 向下 */
}
.flex_J_C {
  justify-content: center;
}
.flex_J_R {
  justify-content: flex-end;
}
.flex_J_SB {
  justify-content: space-between;
}
.flex_J_SA {
  justify-content: space-around;
}
.flex_J_SE {
  justify-content: space-evenly;
}
.flex_j_T {
  justify-content: flex-start;
}
.grid_col6 {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
}
.grid_col3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
}
.grid_col2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}
.grid_colSpan2 {
  grid-gap: 2vmin;
}
.grid_colSpan1 {
  grid-gap: 1vmin;
}
.flex1 {
  flex: 1;
}
.w9 {
  width: 9%;
}.w12{
  width: 12%;
}
.w14 {
  width: 14%;
}
.w15 {
  width: 15%;
}
.w19 {
  width: 19%;
}
.w20 {
  width: 20%;
}
.w23 {
  width: 23%;
}
.w24 {
  width: 24%;
}
.w25 {
  width: 25%;
}
.w26 {
  width: 26%;
}
.w28 {
  width: 28%;
}
.w30 {
  width: 30%;
}
.w32 {
  width: 32%;
}
.w32max {
  width: 32.5%;
}
.w33 {
  width: 33%;
}
.w36 {
  width: 36%;
}
.w40 {
  width: 40%;
}
.w42 {
  width: 42%;
}
.w44 {
  width: 44%;
}
.w47 {
  width: 47%;
}
.w48 {
  width: 48%;
}
.w49 {
  width: 49%;
}
.w54 {
  width: 54%;
}
.w60 {
  width: 60%;
}
.w100 {
  width: 100% !important;
}
.min_w100 {
  min-width: 100%;
}
.hauto {
  height: auto;
}
.h24 {
  height: 24%;
}
.h25 {
  height: 25%;
}
.h28 {
  height: 28%;
}
.h30 {
  height: 30%;
  min-height: 30%;
}
.h32 {
  height: 32%;
  min-height: 32%;
}
.vh24 {
  height: 25.5vh;
  max-height: 32%;
}
.h40 {
  height: 40%;
}
.h48 {
  height: 48%;
}
.h49 {
  height: 49%;
}
.h50 {
  height: 50%;
}
.h52 {
  height: 52%;
}
.h60 {
  height: 60%;
}
.h70 {
  height: 70%;
}
.h80 {
  height: 80%;
}
.h90 {
  height: 90%;
}
.h100 {
  height: 100%;
}
.vh100 {
  height: 100vh !important;
}
.por {
  position: relative;
}
.over {
  overflow: hidden;
}
.mt_02 {
  margin-top: 0.2vmin;
}
.mr_02 {
  margin-right: 0.2vmin;
}
