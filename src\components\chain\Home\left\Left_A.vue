<!-- 左上 -->
<template>
  <div class="flex flex_col flex_J_SB por list_item h32">
    <p class="NB_foot_4 item_title">企业增长分析</p>
    <div style="min-height: 22vmin; height: 80%" ref="Left_A"></div>
  </div>
</template>

<script setup name="Left_A">
import * as echarts from "echarts";
import { ref, nextTick, onMounted, onBeforeUnmount } from "vue";
let Left_A = ref(null);
onMounted(() => {
  nextTick(() => {
    initChart();
  });
});
let intervalId;
var currentIndex = 0;
onBeforeUnmount(() => {
  if (intervalId) {
    clearTimeout(intervalId);
  }
});
function initChart() {
  clearInterval(intervalId);
  const chart = echarts.init(Left_A.value);
  // 设置图表的配置项和数据
  const option = {
    grid: {
      top: "16%", // 距离容器顶部的距离
      bottom: "0%", // 距离容器底部的距离
      left: "1%",
      right: "1%", // 图例占据右侧的宽度
      containLabel: true,
    },
    legend: {
      orient: "horizontal",
      right: "0%", // 图例在右侧居中显示
      top: "top",
      itemHeight: 14,
      itemWidth: 18,
      itemRadius: 20,
      textStyle: {
        color: "white", // 设置图例文字颜色为白色
      },
    },
    tooltip: {
      trigger: "axis", // 使用 axis 触发类型
      // axisPointer: {
      // type: "shadow", // 设置指示器类型为阴影
      // },
      textStyle: {
        align: "center",
      },
      formatter: function (params) {
        let str = "";
        let firstItem = true;
        params.forEach((item, i) => {
          if (firstItem) {
            str += item.axisValue + "<br/>";
            firstItem = false; // 标记为已处理
          }
          let unit = "";
          if (item.seriesName === "企业数量") {
            unit = "个";
          } else if (item.seriesName === "增长率") {
            unit = "%";
          }
          str +=
            item.marker +
            item.seriesName +
            ": " +
            item.value +
            unit + // 添加单位
            "<br/>";
        });
        return str;
      },
    },
    xAxis: {
      // data: stores.Area_data.A.data.map((item) => item.district),
      data: ["2022年", "2023年", "2024年"],
      type: "category",
      axisLabel: {
        fontSize: 14,
        interval: 0,
        textStyle: {
          color: "white", // 设置刻度线文字颜色为白色
        },
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          type: "dashed", // 设置为虚线样式
          color: "rgba(244, 244, 244,0.2)",
        },
      },
    },
    // title: {
    //   text: "(个)",
    //   textStyle: {
    //     color: "#fff",
    //     fontSize: 13,
    //   },
    //   left: "0%",
    //   top: "1%",
    // },
    yAxis: {
      type: "value",
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false, // 刻度线
      },
      axisLabel: {
        textStyle: {
          color: "white", // 设置刻度线文字颜色为白色
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "rgba(77, 98, 152,0.6)",
          type: "dashed", // 设置为虚线样式
        },
      },
    },
    series: [
      {
        data: [92, 107, 124],
        name: "企业数量",
        type: "bar",
        barWidth: "25", // 柱子的宽度
        label: {
          formatter: "{c}",
          show: true,
          position: "top",
          textStyle: {
            color: "#fff",
          },
        },
        // barCategoryGap: "0",
        // barGap: 0.5,
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "rgba(4, 27, 68,0.4)",
              },
              {
                offset: 1,
                color: "#0f82f8",
              },
            ]),
            borderColor: "#0f82f8",
          },
        },
      },
      {
        data: [15.7, 15.7, 15.7],
        name: "增长率",
        type: "line",
        lineStyle: {
          color: "#fcac00",
        },
        itemStyle: {
          color: "#fcac00",
        },
        symbol: "emptyCircle", // 小点点形状设置为空心圆
        label: {
          formatter: "{c}%",
          show: true,
          position: "top",
          textStyle: {
            color: "#fff",
          },
        },
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: "rgba(252, 172, 0, 0.2)",
                },
                {
                  offset: 1,
                  color: "rgba(2, 29, 68,0.03)",
                },
              ],
              false
            ),
          },
        },
      },
    ],
  };
  chart.setOption(option);
  intervalId = setInterval(() => {
    var dataLen = option.series[0].data.length;
    currentIndex = (currentIndex + 1) % dataLen;
    option.series.forEach((series, index) => {
      chart.dispatchAction({
        type: "highlight",
        seriesIndex: index,
        dataIndex: currentIndex,
      });

      // Show tooltip for each series
      chart.dispatchAction({
        type: "showTip",
        seriesIndex: index,
        dataIndex: currentIndex,
      });
    });
  }, 4000);
}
</script>

<style scoped lang="scss"></style>
