<!-- 左二 -->
<template>
  <div class="flex flex_col flex_J_SB por list_item h32">
    <p class="NB_foot_4 item_title">服务数量</p>
    <div style="width: 100%; height: 85%" ref="Right_B"></div>
  </div>
</template>

<script setup name="Right_B">
import * as echarts from "echarts";
import { ref, onMounted } from "vue";
import useCounter from "@/stores/chain/chain.js";
let Right_B = ref(null);
var myChart;
onMounted(() => {
  getData();
  // list.value = stores.need_data;
});
let X_name = [
  { name: "“数智工厂”建设数量", value: 10, rate: 0.4 },
  { name: "数字化转型产品", value: 30, rate: 0.8 },
  { name: "整体服务企业数量", value: 50, rate: 0.8 },
  { name: "企业接入数量", value: 800, rate: 0.2 },

  // { name: "D轮", value: 12, rate: 12.5 },
  // { name: "股权/战略融资", value: 47, rate: 12.5 },
  // { name: "其他", value: 211, rate: 12.5 },
];
let intervalId;
function resizeHandler() {
  myChart.resize();
}
async function getData() {
  window.removeEventListener("resize", resizeHandler);
  window.addEventListener("resize", resizeHandler);
  clearInterval(intervalId);
  if (myChart != null && myChart != "" && myChart != undefined)
    myChart.dispose();
  myChart = echarts.init(Right_B.value);

  const option = {
    grid: {
      left: "0%",
      top: "4%",
      bottom: "4%",
      right: "8%",
      containLabel: true,
    },
    yAxis: {
      type: "category",
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        // formatter: function (e) {
        // console.log(" getData ~ val:", e);
        // },
        color: "#fff",
        fontSize: 12,
      },
      splitArea: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      data: X_name.map((item) => item.name),
    },

    xAxis: {
      type: "value",
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: "#2f3640",
        },
      },
      splitLine: {
        show: false,
        lineStyle: {
          color: "#2f3640 ",
        },
      },
      axisLabel: { fontSize: 12, show: true, color: "#fff" },
    },
    series: {
      name: "",
      type: "bar",
      itemStyle: {
        normal: {
          show: true,
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            {
              offset: 0,
              color: "#4d7ef6",
            },
            {
              offset: 1,
              color: "#01B4FF",
            },
          ]),
          // barBorderRadius: 10,
          borderWidth: 0,
        },
        emphasis: {
          shadowBlur: 15,
          shadowColor: "rgba(105,123, 214, 0.7)",
        },
      },
      zlevel: 2,
      barWidth: "16px",
      data: X_name,
      // data: deepList,
      label: {
        normal: {
          formatter: "{c}个",
          color: "#6BD3FD",
          show: true,
          position: "right",
          textStyle: {
            fontSize: 10,
            fontWeight: "bold",
            fontFamily: "PingFangSC",
          },
        },
      },
    },
  };

  myChart.setOption(option, true);
}
</script>
