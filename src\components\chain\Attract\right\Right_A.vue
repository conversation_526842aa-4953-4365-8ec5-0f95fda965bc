<!-- 右上 -->
<template>
  <div class="flex flex_col flex_J_SB por list_item h32 wanqiHomeAitem">
    <p class="NB_foot_4 item_title">重点领域分析</p>
    <div style="width: 100%; height: 85%" ref="Right_A"></div>
  </div>
</template>

<script setup name="Right_A">
import * as echarts from "echarts";
import { ref, onMounted } from "vue";
let Right_A = ref(null);
var myChart_A;
onMounted(() => {
  getData();
});
function resizeHandler_A() {
  myChart_A.resize();
}
async function getData() {
  var currentIndex = 0;
  window.removeEventListener("resize", resizeHandler_A);
  window.addEventListener("resize", resizeHandler_A);
  let intervalId;
  clearInterval(intervalId);
  if (myChart_A != null && myChart_A != "" && myChart_A != undefined)
    myChart_A.dispose();
  myChart_A = echarts.init(Right_A.value);

  var option = {
    grid: {
      left: "5%", // 调整左边距以容纳左侧Y轴名称
      top: "20%",
      bottom: "14%",
      right: "5%", // 调整右边距以容纳右侧Y轴名称
      containLabel: true, // 确保标签包含在grid内
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        textStyle: {
          color: "#fff",
        },
      },
      formatter: function (params) {
        let str = params[0].axisValue + "<br/>";
        // 确保只处理产品总产值系列
        params.forEach((item) => {
          if (item.seriesName === "产品总产值") {
            str += `${item.marker} ${item.seriesName}: ${item.value} 万元<br/>`;
          }
        });
        return str;
      },
    },
    legend: {
      data: ["产品总产值"], // 更新图例数据
      orient: "horizontal",
      top: "top",
      left: "center",
      itemHeight: 14,
      itemWidth: 18,
      itemGap: 20,
      textStyle: {
        color: "white",
      },
    },
    xAxis: {
      type: "category",
      data: ["LED照明", "触控显示", "电子电路板", "智能终端"], // 更新X轴数据
      axisLine: {
        show: true,
        lineStyle: {
          type: "dashed",
          color: "rgba(244, 244, 244,0.1)",
        },
      },
      axisTick: {
        show: false,
      },
      offset: 4,
      axisLabel: {
        interval: 0,
        textStyle: {
          color: "white",
        },
        align: "center",
      },
    },
    yAxis: [
      {
        // 左侧Y轴 - 产品总产值
        type: "value",
        name: "(万元)",
        nameTextStyle: {
          color: "white",
          align: "right",
        },
        // min: 0,
        min: 100, // 根据图片调整最大值
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          fontSize: 14,
          textStyle: {
            color: "white",
          },
          formatter: function (value) {
            // 格式化Y轴标签，添加千位分隔符
            return value.toLocaleString();
          },
        },
        splitLine: {
          show: true,
          lineStyle: { type: "dashed", color: "rgba(77, 128, 254, 0.2)" },
        },
      },
    ],
    series: [
      {
        name: "产品总产值",
        type: "line",
        yAxisIndex: 0, // 对应左侧Y轴
        data: [1100, 350, 150, 210], // 根据图片估算数据
        smooth: false, // 图片中是阶梯线，不是平滑曲线
        step: "end", // 设置为阶梯线
        symbol: "circle", // 节点为圆点
        symbolSize: 8,
        lineStyle: {
          color: "#00CED1", // 青色线条
          width: 2,
        },
        itemStyle: {
          color: "#00CED1", // 节点颜色
        },
        areaStyle: {
          // 区域填充
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: "rgba(0, 206, 209, 0.4)", // 区域渐变起始色 (带透明度)
            },
            {
              offset: 1,
              color: "rgba(0, 206, 209, 0)", // 区域渐变结束色 (全透明)
            },
          ]),
        },
        label: {
          show: false, // 图片中折线图没有显示数值
        },
      },
    ],
  };
  myChart_A.setOption(option);

  // 调整自动高亮逻辑以适配新的系列
  intervalId = setInterval(() => {
    var dataLen = option.series[0].data.length; // 使用第一个系列的数据长度
    currentIndex = (currentIndex + 1) % dataLen;

    // 遍历所有系列进行高亮和显示tooltip
    option.series.forEach((series, index) => {
      myChart_A.dispatchAction({
        type: "highlight",
        seriesIndex: index,
        dataIndex: currentIndex,
      });

      myChart_A.dispatchAction({
        type: "showTip",
        seriesIndex: index,
        dataIndex: currentIndex,
      });
    });
  }, 4000);
}
</script>
<style lang="scss" scoped></style>
