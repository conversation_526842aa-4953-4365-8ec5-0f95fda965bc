<!-- 左三 -->
<template>
  <div class="flex flex_col flex_J_SB por list_item h32">
    <p class="NB_foot_4 item_title">研发机构</p>
    <div ref="Left_C" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script setup name="Left_C">
import "echarts-gl";
import * as echarts from "echarts";
import { ref, onMounted } from "vue";
import useCounter from "@/stores/chain/chain.js";

var myChart;
let Left_C = ref(null);
onMounted(() => {
  init();
});
function resizeHandler() {
  myChart.resize();
}
/* 
  吉安市数字视听有省级及以上研究机构11家。
  其中工程研究中心数量最多，有7家， 占比为63.64%；
  其次为企业技术中心，有3家， 占比为27.27%。
 */
function init() {
  window.removeEventListener("resize", resizeHandler);
  window.addEventListener("resize", resizeHandler);
  myChart = echarts.init(Left_C.value);
  let colors = ["#0e41fc", "#36fee9", "#07c3f9", "#0195ff", "#006cff"];
  const optionsData = [
    {
      name: "其他研究机构",
      value: 1,
      num: 1,
      path: 9.09,
    },
    {
      name: "工程研究中心",
      value: 7,
      num: 7,
      path: 63.64,
    },
    {
      name: "企业技术中心",
      value: 3,
      num: 3,
      path: 27.27,
    },
  ];

  function getParametricEquation(
    startRatio,
    endRatio,
    isSelected,
    isHovered,
    k,
    height,
    i
  ) {
    // 计算
    let midRatio = (startRatio + endRatio) / 2;

    let startRadian = startRatio * Math.PI * 2;
    let endRadian = endRatio * Math.PI * 2;
    let midRadian = midRatio * Math.PI * 2;

    // 如果只有一个扇形，则不实现选中效果。
    if (startRatio === 0 && endRatio === 1) {
      isSelected = false;
    }

    // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
    k = typeof k !== "undefined" ? k : 1 / 3;

    // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
    let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
    let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;
    let offsetZ = i == 1 ? 2 : 0;
    // 计算高亮效果的放大比例（未高亮，则比例为 1）
    let hoverRate = isHovered ? 1.05 : 1;

    // 返回曲面参数方程
    return {
      u: {
        min: -Math.PI,
        max: Math.PI * 3,
        step: Math.PI / 32,
      },

      v: {
        min: 0,
        max: Math.PI * 2,
        step: Math.PI / 20,
      },

      x: function (u, v) {
        if (u < startRadian) {
          return (
            offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
          );
        }
        if (u > endRadian) {
          return (
            offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
          );
        }
        return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
      },

      y: function (u, v) {
        if (u < startRadian) {
          return (
            offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
          );
        }
        if (u > endRadian) {
          return (
            offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
          );
        }
        return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
      },

      z: function (u, v) {
        if (u < -Math.PI * 0.5) {
          return Math.sin(u);
        }
        if (u > Math.PI * 2.5) {
          return Math.sin(u);
        }
        return Math.sin(v) > 0 ? 1 * height : -1;
      },
    };
  }
  // 生成模拟 3D 饼图的配置项
  function getPie3D(pieData, internalDiameterRatio) {
    let series = [];
    let sumValue = 0;
    let startValue = 0;
    let endValue = 0;
    let legendData = [];
    let k =
      typeof internalDiameterRatio !== "undefined"
        ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
        : 1 / 3;

    // 为每一个饼图数据，生成一个 series-surface 配置
    for (let i = 0; i < pieData.length; i++) {
      sumValue += pieData[i].value;

      let seriesItem = {
        name:
          typeof pieData[i].name === "undefined"
            ? `series${i}`
            : pieData[i].name,
        type: "surface",
        parametric: true,
        wireframe: {
          show: false,
        },
        pieData: pieData[i],
        itemStyle: {
          color: colors[i], // 自定义颜色
          opacity: "1",
        },
        pieStatus: {
          selected: false,
          hovered: false,
          k: k,
        },
      };

      if (typeof pieData[i].itemStyle != "undefined") {
        let itemStyle = {};

        typeof pieData[i].itemStyle.color != "undefined"
          ? (itemStyle.color = pieData[i].itemStyle.color)
          : null;
        typeof pieData[i].itemStyle.opacity != "undefined"
          ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
          : null;

        seriesItem.itemStyle = itemStyle;
      }
      series.push(seriesItem);
    }

    // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
    // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
    for (let i = 0; i < series.length; i++) {
      endValue = startValue + series[i].pieData.value;
      // console.log(series[i]);
      series[i].pieData.startRatio = startValue / sumValue;
      series[i].pieData.endRatio = endValue / sumValue;
      series[i].parametricEquation = getParametricEquation(
        series[i].pieData.startRatio,
        series[i].pieData.endRatio,
        false,
        false,
        k,
        // 调整扇形高度
        // i === 0 ? 10 : 10,
        i + 4 + i,
        series[i].pieData.value
      );

      startValue = endValue;

      legendData.push(series[i].name);
    }
    return series;
  }

  const series = getPie3D(optionsData, 0.6);
  // 可做为调整内环大小 0为实心圆饼图，大于0 小于1 为圆环
  series.push({
    name: "pie2d",
    type: "pie",
    label: {
      show: true,
      opacity: 1,
      fontSize: 8,
      lineHeight: 0,
      textStyle: {
        fontSize: 8,
        color: "#fff",
      },
    },
    labelLine: { length: 0, length2: 0 },
    startAngle: 240, //起始角度，支持范围[0, 360]。
    clockwise: false, //饼图的扇区是否是顺时针排布。上述这两项配置主要是为了对齐3d的样式
    radius: ["50%", "50%"],
    center: ["50%", "30%"],
    data: optionsData,
    itemStyle: {
      opacity: 0,
    },
  });
  let option = {
    legend: {
      type: "scroll",
      top: "bottom",
      itemHeight: 10,
      itemWidth: 10,
      itemRadius: 10,
      textStyle: {
        color: "#fff",
        fontSize: 13,
      },
      orient: "horizontal",
      pageIconColor: "#306df8",
      pageTextStyle: {
        color: "#999",
      },
      show: true,
      data: optionsData,
      left: "center",
    },
    animation: true,
    tooltip: {
      trigger: "item",
      axisPointer: {
        type: "shadow",
        crossStyle: {
          color: "#999",
        },
      },
      formatter: function (e) {
        if (optionsData[e.seriesIndex] == undefined) {
          return;
        } else {
          return (
            e.seriesName +
            "<br/>" +
            e.marker +
            " " +
            optionsData[e.seriesIndex].path +
            "%"
          );
        }
      },
    },
    labelLine: {
      show: true,
      normal: {
        lineStyle: {
          width: 1,
          color: "rgba(255,255,255,0.7)",
        },
      },
    },
    label: {
      normal: {
        show: true,
        color: "#fff",
        formatter: ["{b|{b}}", "{c|{c}}{b|台}", "{d|{d}%}"].join("\n"), // 用\n来换行
        rich: {
          b: {
            color: "#fff",
            lineHeight: 25,
            align: "left",
          },
          c: {
            fontSize: 22,
            color: "#fff",
            textShadowColor: "#1c90a6",
            textShadowOffsetX: 0,
            textShadowOffsetY: 2,
            textShadowBlur: 5,
          },
          d: {
            color: "#fff",
            align: "left",
          },
        },
      },
    },

    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 4,
      left: "center",
      top: "-13%",
      postEffect: {
        // 配置这项会出现锯齿，请自己去查看官方配置有办法解决
        enable: false,
        bloom: {
          enable: true,
          bloomIntensity: 0.1,
        },
        SSAO: {
          enable: true,
          quality: "medium",
          radius: 2,
        },
        temporalSuperSampling: {
          enable: true,
        },
      },
      // environment: "rgba(255,255,255,0)",
      viewControl: {
        distance: 180, //调整视角到主体的距离，类似调整zoom
        alpha: 26, //角度
        beta: 110,
        rotateSensitivity: 0, //设置为0无法旋转
        zoomSensitivity: 0, //设置为0无法缩放
        panSensitivity: 0, //设置为0无法平移
        autoRotate: false, // 自动旋转
      },
    },
    series: series,
  };

  myChart.setOption(option);

  // var currentIndex = 0;
  // var intervalId;
  // intervalId = setInterval(() => {
  //   var dataLen = option.series.length;
  //   // return;
  //   currentIndex = (currentIndex + 1) % dataLen;

  //   option.series.forEach((series, index) => {
  //     myChart.dispatchAction({
  //       type: "highlight",
  //       seriesIndex: index,
  //       dataIndex: currentIndex,
  //     });

  //     // Show tooltip for each series
  //     myChart.dispatchAction({
  //       type: "showTip",
  //       seriesIndex: index,
  //       dataIndex: currentIndex,
  //     });
  //   });
  // }, 4000);
  window.addEventListener("resize", () => {
    myChart.resize();
  });
}
</script>

<style scoped lang="scss"></style>
