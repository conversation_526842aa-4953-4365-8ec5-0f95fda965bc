import {
  createRouter,
  createWebHistory,
  createWebHashHistory,
} from "vue-router";
import routes from "./RouterData.js";

const router = createRouter({
  // history: createWebHistory(process.env.BASE_URL),
  history: createWebHashHistory(process.env.BASE_URL),
  routes,
});

router.beforeEach((to, from, next) => {
  document.title = to.meta.user || "默认标题";
  next();
  //   let token = JSON.parse(localStorage.getItem("nmd_newToken") || "false");
  //   // 判断目标路由是否需要身份验证
  //   if (to.meta.requiresAuth) {
  //     if (token) {
  //       // 如果有 token，允许访问目标路由
  //       next();
  //     } else {
  //       // 如果没有 token，跳转到登录页面
  //       next({ name: "Login" });
  //     }
  //   } else {
  //     // 如果目标路由不需要身份验证，允许访问
  //     next();
  //   }
});
export default router;
