import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import pinia from "./stores";
import vue3SeamlessScroll from "vue3-seamless-scroll";
import Antd from "ant-design-vue";

import "echarts-gl"; //有的甲方项目不支持webgl
import "@/assets/style/base.css";
import "@/assets/style/animation.scss";
import "@/assets/style/dependency.css";

import "@/assets/icon/iconfont.css";

import "@/utils/forbidden.js";

createApp(App)
  .use(router)
  .use(Antd)
  .use(pinia)
  .use(vue3SeamlessScroll)
  .mount("#app");
