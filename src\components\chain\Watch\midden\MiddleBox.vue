<!--  -->
<template>
  <div
    style="margin: 0 1.7%"
    class="flex flex_col flex_J_SB por list_item h32 WatchMiddleBox"
  >
    <p class="NB_foot_4 item_title">政策支持</p>

    <div class="table_body flex1 over">
      <vue3-seamless-scroll
        direction="up"
        :hover="true"
        :isWatch="true"
        :limitScrollNum="right_one_list.length"
        :singleLine="true"
        :list="right_one_list"
        :step="0.4"
        class="DongTable flex flex_col"
      >
        <ul class="flex flex_col flex_J_SB">
          <li
            v-for="(item, i) in right_one_list"
            :key="i"
            class="flex flex_J_SB item_row tr"
            :class="[i % 2 != 1 ? 'row_j' : 'row_t']"
          >
            <div class="flex flex_A_C fs14 table_headC body_b">
              {{ item.A || "暂未填写" }}
            </div>
          </li>
        </ul>
      </vue3-seamless-scroll>
    </div>
  </div>
</template>

<script setup name="MiddleBox">
import { onMounted, ref } from "vue";
import useCounter from "@/stores/chain/chain.js";
let right_one_list = ref([]);
onMounted(() => {
  right_one_list.value = [
    {
      A: "吉安市“十四五”电子信息产业发展规划-政策文件",
    },
    {
      A: "吉安市电子信息产业链现代化建设行动方案-政策文件",
    },
    {
      A: "吉安市“招才引智”人才生态体系建设见闻-政策文件",
    },
    {
      A: "电子信息制造业数字化转型实施方案-政策文件",
    },
    {
      A: "吉安市“十四五”电子信息产业发展规划-政策文件",
    },
    {
      A: "吉安市电子信息产业链现代化建设行动方案-政策文件",
    },
    {
      A: "吉安市“招才引智”人才生态体系建设见闻-政策文件",
    },
    {
      A: "电子信息制造业数字化转型实施方案-政策文件",
    },
    {
      A: "吉安市“十四五”电子信息产业发展规划-政策文件",
    },
    {
      A: "吉安市电子信息产业链现代化建设行动方案-政策文件",
    },
    {
      A: "吉安市“招才引智”人才生态体系建设见闻-政策文件",
    },
    {
      A: "电子信息制造业数字化转型实施方案-政策文件",
    },
  ];
});
</script>

<style scoped lang="scss">
.WatchMiddleBox {
  .table_headC {
    width: 100%;
  }
  .table_body {
    .item_row {
      height: 34px;
      border: 1px solid #0c83fa;
      margin-bottom: 12px;
      position: relative;
      border-radius: 2px;
      overflow: hidden;
      padding-left: 1.9vmin;
      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 4px;
        background-color: #0c83fa;
      }
      &:hover {
        background-color: rgba(135, 206, 235, 0.2);
      }
    }
    ::v-deep .DongTable {
      & > div {
        display: flex;
        flex-direction: column;
        will-change: transform;
      }
    }
    .row_t {
      color: #718bbd;
    }
    .row_j {
      background: rgba(1, 91, 255, 0.2);
      background: radial-gradient(
        circle,
        rgba(26, 55, 93, 0.6) 40%,
        rgba(14, 29, 61, 0.1) 100%
      );
    }
  }
}
</style>
