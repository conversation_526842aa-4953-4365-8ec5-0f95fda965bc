import axios from "axios";

//创建axios实例 链万企
const service = axios.create({
  //拿到线上接口地址
  // baseURL: "http://192.168.0.77:8080/",
  baseURL: "https://www.lianwanqi.com/prod-api/",
  settimeout: 5000000, //超时时间s
});

//请求拦截
service.interceptors.request.use(
  (config) => {
    config.headers["Authorization"] =
      "Bearer " + sessionStorage.getItem("chain_token");
    return config; //记得一定要 返回config
  },
  (error) => {
    return Pormise.reject(error);
  }
);

//响应拦截
service.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    // console.log(error);
    return Promise.reject(error);
  }
);

export default service;
