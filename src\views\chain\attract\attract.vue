<!-- 产业招商 -->
<template>
  <ul class="flex wc flex_J_SB flex1 h80 animate__animated animate__zoomIn">
    <li class="main_item flex flex_col flex_J_SB h100">
      <Left_A />
      <Left_B />
      <Left_C />
    </li>
    <li class="flex1 por flex flex_col flex_J_SB h100">
      <MapModule class="wc" />
      <MiddleBox />
    </li>
    <li class="main_item flex flex_col flex_J_SB h100">
      <Right_A />
      <Right_B />
      <Right_C />
    </li>
  </ul>
</template>

<script setup name="watch">
import Left_A from "@/components/chain/Attract/left/Left_A.vue";
import Left_B from "@/components/chain/Attract/left/Left_B.vue";
import Left_C from "@/components/chain/Attract/left/Left_C.vue";
import MapModule from "@/components/chain/Attract/midden/New_Map.vue";
import MiddleBox from "@/components/chain/Attract/midden/MiddleBox.vue";
import Right_A from "@/components/chain/Attract/right/Right_A.vue";
import Right_B from "@/components/chain/Attract/right/Right_B.vue";
import Right_C from "@/components/chain/Attract/right/Right_C.vue";
</script>
