<!-- 左二 -->
<template>
  <div class="flex flex_col flex_J_SB por list_item h32">
    <p class="NB_foot_4 item_title">产业创新</p>
    <div style="width: 100%; height: 85%" ref="Right_B"></div>
  </div>
</template>

<script setup name="Right_B">
import * as echarts from "echarts";
import { ref, onMounted } from "vue";
// import useCounter from "@/stores/chain/chain.js"; // 移除不再需要的导入
let Right_B = ref(null);
var myChart;
onMounted(() => {
  getData();
});
// 移除不再需要的 X_name 数组
let intervalId;
function resizeHandler() {
  myChart.resize();
}
async function getData() {
  var currentIndex = 0; // 用于自动高亮
  window.removeEventListener("resize", resizeHandler);
  window.addEventListener("resize", resizeHandler);
  clearInterval(intervalId);
  if (myChart != null && myChart != "" && myChart != undefined)
    myChart.dispose();
  myChart = echarts.init(Right_B.value);

  const option = {
    grid: {
      left: "5%", // 调整左边距
      top: "20%", // 留出空间给图例
      bottom: "14%",
      right: "5%", // 调整右边距
      containLabel: true,
    },
    tooltip: {
      trigger: "item", // 散点图通常使用 'item' 触发
      axisPointer: {
        type: "none", // 移除轴指示器类型
      },
      formatter: function (params) {
        // 格式化提示框内容
        return `${params.seriesName}<br/>${params.name}年: ${params.value[1]} 项`;
      },
    },
    legend: {
      data: ["注册商标", "发明专利", "软件著作"], // 更新图例数据
      orient: "horizontal",
      top: "top",
      left: "center",
      itemHeight: 14,
      itemWidth: 18,
      itemGap: 20,
      textStyle: {
        color: "white",
      },
    },
    xAxis: {
      type: "category", // X轴类型改为 'category'
      data: ["2022", "2023", "2024"], // 更新X轴数据为指定年份
      axisLine: {
        show: true,
        lineStyle: {
          type: "dashed",
          color: "rgba(244, 244, 244,0.1)",
        },
      },
      axisTick: {
        show: false,
      },
      offset: 4,
      axisLabel: {
        interval: 0,
        textStyle: {
          color: "white",
        },
        align: "center",
      },
    },
    yAxis: {
      type: "value", // Y轴类型改为 'value'
      name: "(项)", // Y轴名称
      nameTextStyle: {
        color: "white",
        align: "right",
      },
      min: 0,
      max: 3000, // 根据图片调整最大值
      interval: 500, // 根据图片调整间隔
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        fontSize: 14,
        textStyle: {
          color: "white",
        },
      },
      splitLine: {
        show: true,
        lineStyle: { type: "dashed", color: "rgba(77, 128, 254, 0.2)" },
      },
    },
    series: [
      {
        name: "注册商标",
        type: "scatter", // 散点图
        symbol: "circle",
        symbolSize: function (data) {
          return data[1] / 90; // 调整除数，使气泡变小
        },
        itemStyle: {
          opacity: 0.8,
          shadowBlur: 10,
          shadowColor: "rgba(0, 123, 255, 0.8)", // 蓝色光晕
          color: "#007bff", // 蓝色
        },
        data: [
          [0, 1521], // 2022年数据 (对应图片中2021年的数据)
          [1, 2268], // 2023年数据 (对应图片中2022年的数据)
          [2, 2572], // 2024年数据 (对应图片中2023年的数据)
        ],
      },
      {
        name: "发明专利",
        type: "scatter",
        symbol: "circle",
        symbolSize: function (data) {
          return data[1] / 80; // 调整除数，使气泡变小
        },
        itemStyle: {
          opacity: 0.8,
          shadowBlur: 10,
          shadowColor: "rgba(0, 206, 209, 0.8)", // 青色光晕
          color: "#00CED1", // 青色
        },
        data: [
          [0, 971], // 2022年数据
          [1, 1172], // 2023年数据
          [2, 1325], // 2024年数据
        ],
      },
      {
        name: "软件著作",
        type: "scatter",
        symbol: "circle",
        symbolSize: function (data) {
          return data[1] / 46; // 调整除数，使气泡变小
        },
        itemStyle: {
          opacity: 0.8,
          shadowBlur: 10,
          shadowColor: "rgba(255, 165, 0, 0.8)", // 橙色光晕
          color: "#FFA500", // 橙色
        },
        data: [
          [0, 524], // 2022年数据
          [1, 761], // 2023年数据
          [2, 860], // 2024年数据
        ],
      },
    ],
  };

  myChart.setOption(option, true);

  // 调整自动高亮逻辑以适配新的系列
  intervalId = setInterval(() => {
    // 遍历所有系列进行高亮和显示tooltip
    option.series.forEach((series, seriesIndex) => {
      // 对于每个系列，高亮当前年份的数据点
      myChart.dispatchAction({
        type: "highlight",
        seriesIndex: seriesIndex,
        dataIndex: currentIndex,
      });

      // 显示tooltip
      myChart.dispatchAction({
        type: "showTip",
        seriesIndex: seriesIndex,
        dataIndex: currentIndex,
      });
    });

    // 取消前一个高亮
    const prevIndex =
      (currentIndex - 1 + option.xAxis.data.length) % option.xAxis.data.length;
    option.series.forEach((series, seriesIndex) => {
      myChart.dispatchAction({
        type: "downplay",
        seriesIndex: seriesIndex,
        dataIndex: prevIndex,
      });
    });

    currentIndex = (currentIndex + 1) % option.xAxis.data.length;
  }, 4000);
}
</script>

<style scoped lang="scss"></style>
