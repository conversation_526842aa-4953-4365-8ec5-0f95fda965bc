<!--  -->
<template>
  <div style="margin: 0 1.7%" class="flex flex_col flex_J_SB por list_item h32">
    <p class="NB_foot_4 item_title">企业清单</p>
    <div class="table_head flex flex_J_SB">
      <div
        class="flex flex_J_C flex_A_C table_headA fs14- font_noWarp text_noWrapA NB_foot_3"
      >
        公司名称
      </div>
      <div
        class="flex flex_J_C flex_A_C table_headB tc fs14- font_noWarp text_noWrapA NB_foot_3"
      >
        公司类型
      </div>
      <div
        class="flex flex_J_C flex_A_C fs14 table_headB tc por head2 NB_foot_3"
      >
        法定代表人
      </div>
      <div class="flex flex_J_C flex_A_C fs14 table_headC tc text_l NB_foot_3">
        注册资本(万元)
      </div>
      <div class="flex flex_J_C flex_A_C fs14 table_headC tc text_l NB_foot_3">
        成立时间
      </div>
    </div>
    <div class="table_body flex1 over">
      <vue3-seamless-scroll
        direction="up"
        :hover="true"
        :isWatch="true"
        :limitScrollNum="right_one_list.length"
        :singleLine="true"
        :list="right_one_list"
        :step="0.4"
        class="DongTable flex flex_col"
      >
        <ul class="flex flex_col flex_J_SB">
          <li
            v-for="(item, i) in right_one_list"
            :key="i"
            class="flex flex_J_SB item_row tr"
            :class="[i % 2 != 1 ? 'row_j' : 'row_t']"
          >
            <div class="flex flex_J_C flex_A_C table_headA">
              <!-- <span class="w100 tc fs14- text_noWrapA">
                <span
                  v-if="i <= 2"
                  class="iconfont"
                  :class="[
                    i == 0
                      ? 'icon-paihang1 fc_12 fs18'
                      : i == 1
                      ? 'icon-paihang3beifen fc_13 fs18'
                      : i === 2
                      ? ' icon-paihang3beifen2 fc_14 fs18'
                      : '',
                  ]"
                ></span>
                <span v-else>{{ i + 1 }}</span>
              </span> -->
              <span class="w100 fs14- tc text_noWrapA">
                {{ item.A || "暂未填写" }}
              </span>
            </div>
            <div class="flex flex_A_C fs14- table_headB body_b">
              <span class="w100 tc text_noWrapA">
                {{ item.B || "暂未填写" }}
              </span>
            </div>
            <div class="flex flex_A_C fs14- table_headB body_b">
              <span class="w100 tc text_noWrapA">
                {{ item.C || "暂未填写" }}
              </span>
            </div>
            <div class="flex flex_J_C flex_A_C fs14 table_headC body_b">
              {{ item.E || "暂未填写" }}
            </div>
            <div class="flex flex_J_C flex_A_C fs14 table_headC body_b">
              {{ item.D || "暂未填写" }}
            </div>
          </li>
        </ul>
      </vue3-seamless-scroll>
    </div>
  </div>
</template>

<script setup name="MiddleBox">
import { onMounted, ref } from "vue";
import useCounter from "@/stores/chain/chain.js";
let right_one_list = ref([]);
onMounted(() => {
  right_one_list.value = [
    {
      A: "江西蓝微电子科技有限公司",
      B: "已私募融资公司,非挂牌上市公司",
      C: "彭庶瑶",
      D: "2010-03-24",
      E: "4987.5",
    },
    {
      A: "江西红板科技股份有限公司",
      B: "已私募融资公司,非挂牌上市公司,拟上市公司",
      C: "叶森然",
      D: "2005-10-17",
      E: "65375.36",
    },
    {
      A: "遂川县四联机械有限责任公司",
      B: "四板公司",
      C: "尹智",
      D: "2009-06-09",
      E: "400",
    },
    {
      A: "江西量一光电科技有限公司",
      B: "非挂牌上市公司",
      C: "廖昆",
      D: "2010-12-20",
      E: "1000",
    },
    {
      A: "宿迁友特照明工程有限公司吉安分公司",
      B: "非挂牌上市公司",
      C: "梁永甜",
      D: "2021-09-03",
      E: "--",
    },
    {
      A: "吉安茂强照明科技有限公司",
      B: "非挂牌上市公司",
      C: "刘强",
      D: "2014-01-26",
      E: "600",
    },
    {
      A: "江西唯尔佳电子科技有限公司",
      B: "非挂牌上市公司",
      C: "黄小星",
      D: "2012-08-15",
      E: "200",
    },
    {
      A: "吉安市艾吉特道路照明工程有限公司",
      B: "非挂牌上市公司",
      C: "戴新华",
      D: "2014-11-25",
      E: "2000",
    },
    {
      A: "吉安蓝吉尔蓝宝石有限公司",
      B: "非挂牌上市公司",
      C: "曾智勇",
      D: "2016-08-12",
      E: "300",
    },
    {
      A: "江西省弘毅服明工程有限公司",
      B: "非挂牌上市公司",
      C: "阙丽平",
      D: "2014-11-03",
      E: "1000",
    },
    {
      A: "江西省志亮照明工程有限公司",
      B: "非挂牌上市公司",
      C: "肖德志",
      D: "2023-02-09",
      E: "200",
    },
    {
      A: "江西圣烨照明工程有限公司",
      B: "非挂牌上市公司",
      C: "高爽",
      D: "2021-11-24",
      E: "200",
    },
    {
      A: "吉安市吉森照明工程有限公司",
      B: "非挂牌上市公司",
      C: "王俊",
      D: "2017-05-27",
      E: "100",
    },
    {
      A: "吉安市嘉昱实业有限公司",
      B: "非挂牌上市公司",
      C: "刘蜗",
      D: "2002-11-26",
      E: "500",
    },
    {
      A: "吉安旭红照明工程有限公司",
      B: "非挂牌上市公司",
      C: "刘桂群",
      D: "2021-07-14",
      E: "200",
    },
    {
      A: "吉安市成杰照明工程有限公司吉安市青原区分公司",
      B: "非挂牌上市公司",
      C: "营强",
      D: "2022-11-23",
      E: "--",
    },
    {
      A: "泰和槙宫明照明工程有限公司",
      B: "非挂牌上市公司",
      C: "刘克湛",
      D: "2017-10-17",
      E: "200",
    },
    {
      A: "吉安喜光服明工程有限公司",
      B: "非挂牌上市公司",
      C: "何顺香",
      D: "2017-07-06",
      E: "300",
    },
    {
      A: "江西锋开瀰钠捞照明工程有限公司",
      B: "非挂牌上市公司",
      C: "王娟",
      D: "2020-05-15",
      E: "200",
    },
    {
      A: "吉安贤光照明工程有限公司",
      B: "非挂牌上市公司",
      C: "吴丹",
      D: "2020-09-18",
      E: "100",
    },
  ];
});
</script>

<style scoped lang="scss">
.table_head {
  background-color: #015bff;
  height: 3.2vmin;
  min-height: 30px;
  margin-top: 0.5vmin;
}
.head1 {
  padding-left: 10%;
  &::before {
    content: "";
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1.2px;
    height: 60%;
    background-color: #fff;
  }
}

.table_headA {
  width: 20%;
  text-align: left;
}
.table_headC {
  width: 24%;
}
.table_headB {
  width: 30%;
}
.table_body {
  .item_row {
    height: 34px;
    &:hover {
      background-color: rgba(135, 206, 235, 0.2);
    }
  }
  ::v-deep .DongTable {
    & > div {
      display: flex;
      flex-direction: column;
      will-change: transform;
    }
  }
  .row_t {
    color: #718bbd;
  }
  .row_j {
    background: rgba(1, 91, 255, 0.2);
    background: radial-gradient(
      circle,
      rgba(26, 55, 93, 0.6) 40%,
      rgba(14, 29, 61, 0.1) 100%
    );
  }
}
</style>
