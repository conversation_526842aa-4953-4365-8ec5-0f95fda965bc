<!-- 右上 -->
<template>
  <div class="flex flex_col flex_J_SB por list_item h32 wanqiHomeAitem">
    <p class="NB_foot_4 item_title">设备运行情况</p>
    <div class="flex flex_J_SB flex1 over">
      <div
        v-for="(item, i) in items"
        class="flex flex_col tc h100 flex_J_C w23 por wanqiHomeAitemFlex"
        :key="i"
      >
        <div class="flex w100 flex_col wanqiHomeAitemText">
          <span class="w100 fs14 NB_foot_2">
            {{ item.name }}
          </span>
          <div
            class="flex flex_J_C font_skew fs18 fw9"
            :style="{ color: item.color }"
          >
            <count-up
              :startVal="0"
              :endVal="item.num"
              :duration="3"
              :loop="true"
              :delay="12"
            />
            {{ item.unit }}
          </div>
        </div>
        <div class="w100">
          <img :src="item.url" alt="" class="w100" />
        </div>
        <div
          v-for="son in 8"
          :key="son"
          class="lit wc"
          :style="{
            left: suijiNumFn(120, 30) + 'px',
            width: suijiNumFn(8, 8) + 'px',
            height: suijiNumFn(8,8) + 'px',
            backgroundColor: item.color || '#ff6d34',
            'animation-duration': randomDecimalFn(5.28749, 1.20546) + 's',
            'animation-delay': randomDecimalFn(1.8, 0) + 's',
          }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup name="Right_A">
import { ref, onMounted } from "vue";
import CountUp from "vue-countup-v3";
let items = [
  {
    name: "接入数量",
    num: 123450,
    unit: "",
    url: require("@/assets/image/wanqiHomeAitem1.png"),
    color: "#2e89e0",
  },
  {
    name: "今日进度",
    num: 88,
    unit: "%",
    url: require("@/assets/image/wanqiHomeAitem2.png"),
    color: "#25bda4",
  },
  {
    name: "在线数量",
    num: 123450,
    unit: "",
    url: require("@/assets/image/wanqiHomeAitem3.png"),
    color: "#2dadc6",
  },
  {
    name: "今日活跃",
    num: 88,
    unit: "%",
    url: require("@/assets/image/wanqiHomeAitem4.png"),
    color: "#25bea2",
  },
];
function suijiNumFn(max, min) {
  return Math.floor(Math.random() * (max - min + 1) + min);
}
function randomDecimalFn(max, min) {
  // 计算范围的差值
  const range = max - min;
  // 生成一个0到range的随机小数
  const randomDecimal = Math.random() * range;
  // 将随机小数加到最小值上
  return randomDecimal + min;
}
</script>
<style lang="scss" scoped>
.wanqiHomeAitem {
  .lit {
    z-index: 1;
    height: 50px;
    width: 50px;
    border-radius: 50%;
    position: absolute;
    bottom: -2vmin;
    pointer-events: none;
    transform: translate(-50%, -50%);
    animation: blow 2s ease-in infinite;
    transition: all 0.4s ease;
  }
  @keyframes blow {
    0% {
      transform: translate(-50%, -50%) scale(0.1);
      opacity: 0;
    }
    10% {
      transform: translate(-50%, -50%);
      opacity: 1;
    }
    100% {
      transform: translate(-50%, -20vh);
      opacity: 0;
    }
  }
  .wanqiHomeAitemFlex {
    padding-top: 6%;
  }
  .wanqiHomeAitemText {
    position: absolute;
    top: 24%;
    left: 50%;
    transform: translate(-50%, -50%);
    & > div {
      margin-top: 0.6vmin;
    }
  }
}
</style>
