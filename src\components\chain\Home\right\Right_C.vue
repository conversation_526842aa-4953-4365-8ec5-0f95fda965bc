<!--  -->
<template>
  <div class="flex flex_col flex_j_SB over list_item h32">
    <p class="NB_foot_4 item_title">授权专利分析</p>
    <div style="min-height: 22vmin; height: 80%" ref="Right_C"></div>
  </div>
</template>

<script setup name="Right_C">
import * as echarts from "echarts";
import { ref, nextTick, onMounted, onBeforeUnmount } from "vue";
let intervalId;
let Right_C = ref(null);
var currentIndex = 0;
onMounted(() => {
  nextTick(() => {
    initChart();
  });
});
onBeforeUnmount(() => {
  if (intervalId) {
    clearTimeout(intervalId);
  }
});
function initChart() {
  clearInterval(intervalId);
  const chart = echarts.init(Right_C.value);
  // 设置图表的配置项和数据
  const option = {
    grid: {
      top: "16%", // 距离容器顶部的距离
      bottom: "0%", // 距离容器底部的距离
      left: "1%",
      right: "1%", // 图例占据右侧的宽度
      containLabel: true,
    },
    legend: {
      orient: "horizontal",
      right: "0%", // 图例在右侧居中显示
      top: "top",
      itemHeight: 14,
      itemWidth: 18,
      itemRadius: 20,
      textStyle: {
        color: "white", // 设置图例文字颜色为白色
      },
    },
    tooltip: {
      trigger: "axis", // 使用 axis 触发类型
      // axisPointer: {
      // type: "shadow", // 设置指示器类型为阴影
      // },
      textStyle: {
        align: "center",
      },
      formatter: function (params) {
        let str = "";
        let firstItem = true;
        params.forEach((item, i) => {
          if (firstItem) {
            str += item.axisValue + "<br/>";
            firstItem = false; // 标记为已处理
          }
          let unit = "件";
          if (
            item.seriesName === "企业数量" ||
            item.seriesName === "授权数量"
          ) {
            // 为新系列添加单位判断
            unit = "个";
          } else if (item.seriesName === "增长率") {
            unit = "%";
          }
          str +=
            item.marker +
            item.seriesName +
            ": " +
            item.value +
            unit + // 添加单位
            "<br/>";
        });
        return str;
      },
    },
    xAxis: {
      // data: stores.Area_data.A.data.map((item) => item.district),
      data: ["2022年", "2023年", "2024年"],
      type: "category",
      axisLabel: {
        fontSize: 14,
        interval: 0,
        textStyle: {
          color: "white", // 设置刻度线文字颜色为白色
        },
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        lineStyle: {
          type: "dashed", // 设置为虚线样式
          color: "rgba(244, 244, 244,0.2)",
        },
      },
    },
    // title: {
    //   text: "(个)",
    //   textStyle: {
    //     color: "#fff",
    //     fontSize: 13,
    //   },
    //   left: "0%",
    //   top: "1%",
    // },
    yAxis: {
      type: "value",
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false, // 刻度线
      },
      axisLabel: {
        textStyle: {
          color: "white", // 设置刻度线文字颜色为白色
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "rgba(77, 98, 152,0.6)",
          type: "dashed", // 设置为虚线样式
        },
      },
    },
    series: [
      {
        data: [32, 37, 42],
        name: "发明专利",
        type: "bar",
        barWidth: "15%", // 柱子的宽度
        barCategoryGap: "0",
        barGap: 0.5,
        label: {
          formatter: "{c}件",
          show: true,
          position: "top",
          textStyle: {
            color: "#fff",
          },
        },
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "rgba(4, 27, 68,0.4)",
              },
              {
                offset: 1,
                color: "#0f82f8",
              },
            ]),
            borderColor: "#0f82f8",
          },
        },
      },
      {
        data: [467, 483, 504],
        name: "实用新型专利",
        type: "bar",
        barWidth: "15%", // 柱子的宽度
        barCategoryGap: "0",
        barGap: 0.5,
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "rgba(40, 80, 80, 0.4)", // 调整为更柔和的起始色
              },
              {
                offset: 1,
                color: "#66CDAA", // 调整为更柔和的结束色 (Medium Aquamarine)
              },
            ]),
            borderColor: "#66CDAA", // 边框颜色与结束色保持一致
          },
        },
        label: {
          formatter: "{c}件",
          show: true,
          position: "top",
          textStyle: {
            color: "#fff",
          },
        },
      },
      // 新增的柱子系列
      {
        data: [20, 25, 32], // 示例数据
        name: "外观设计专利", // 新增系列名称
        type: "bar",
        barWidth: "15%", // 柱子的宽度
        barCategoryGap: "0",
        barGap: 0.5,
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "rgba(0, 100, 150, 0.4)", // 深海蓝，半透明
              },
              {
                offset: 1,
                color: "#00CED1", // 暗绿松石色 (Dark Turquoise)，清澈的海水蓝
              },
            ]),
            borderColor: "#00CED1", // 边框颜色与结束色保持一致
          },
        },
        label: {
          formatter: "{c}件",
          show: true,
          position: "top",
          textStyle: {
            color: "#fff",
          },
        },
      },
    ],
  };
  chart.setOption(option);
  intervalId = setInterval(() => {
    var dataLen = option.series[0].data.length;
    currentIndex = (currentIndex + 1) % dataLen;
    option.series.forEach((series, index) => {
      chart.dispatchAction({
        type: "highlight",
        seriesIndex: index,
        dataIndex: currentIndex,
      });

      // Show tooltip for each series
      chart.dispatchAction({
        type: "showTip",
        seriesIndex: index,
        dataIndex: currentIndex,
      });
    });
  }, 4000);
}
</script>

<style scoped lang="scss">
.table_head {
  background-color: #015bff;
  height: 34px;
  min-height: 34px;
  margin-top: 0.8vmin;
}
.head1 {
  padding-left: 10%;
  &::before {
    content: "";
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1.2px;
    height: 60%;
    background-color: #fff;
  }
}

.table_headA {
  width: 46%;
  text-align: left;
}
.table_headC {
  width: 24%;
}
.table_headB {
  width: 30%;
}
.table_body {
  .item_row {
    height: 34px;
    &:hover {
      background-color: rgba(135, 206, 235, 0.2);
    }
  }
  ::v-deep .DongTable {
    & > div {
      display: flex;
      flex-direction: column;
      will-change: transform;
    }
  }
  .row_t {
    color: #718bbd;
  }
  .row_j {
    background: rgba(1, 91, 255, 0.2);
  }
}
</style>
