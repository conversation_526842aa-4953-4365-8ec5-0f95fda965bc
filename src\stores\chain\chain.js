//定义关于counter的store
import { defineStore } from "pinia";
import service from "@/http/chain/request.js";
import API from "@/apis/chain/index.js";
import UTILS from "@/utils/chain/index.js";
/*
  defineStore 是需要传参数的，其中第一个参数是id，就是一个唯一的值，
  简单点说就可以理解成是一个命名空间.
  第二个参数就是一个对象，里面有三个模块需要处理，第一个是 state，
  第二个是 getters，第三个是 actions。
*/
const useChain = defineStore("counter", {
  state: () => ({
    nav_data: [], // nav 数据
    midden_mapData: [], //地图企业数量
    need_data: {}, // 需求类型图表
    resource_data: {}, // 资源图表
    enterprise_data: [], //企业潜力
    Market_data: [], // 平台集聚
    Policy_data: [], // 政策汇聚
    Area_data: {}, //左一资源
    CARD: [], // 中间的卡片
    Tendency: {}, // 增长趋势
    /* 二屏 ↓ */
    Details_Left_A: {}, // 二屏 左1数据
    Details_Left_B: {}, //二平 左2需求数据
    Details_Model_one: {}, //二平 左2需求数据
    Details_two: {}, //二屏  政策集聚
    right_three: [], // 二屏三弟饼图
    right_three_rows: {},
    right_four: [], // 获取二屏企业潜力
    detailsList: {}, // 详情左侧数据
  }),
  actions: {
    // 获取 首页数据
    async GetAllData() {
      await LoginFn();
      const [A, DATA, LEFTBC, ENTERPRISE, MARKET, POLICY, CARD, Tendency] =
        await Promise.all([
          UTILS.getNumByDistrict(), // A地图数据
          UTILS.GET_NAVDATA_FN(),
          UTILS.GET_LEFTBC_Fn(), // 左侧bc柱子
          UTILS.GET_Enterprise_Fn(), // 右侧二卡片 ENTERPRISE
          UTILS.GET_Market_Fn(), //  右二平台集聚 MARKET
          service({
            url: `https://uuc.ningmengdou.com/prod-api/uuc/authorize/company/list?page=1&limit=100`,
            method: "GET",
          }), // 右3入驻企业
          UTILS.GET_CardData(), // 中间的卡片
          UTILS.GET_TendencyData(), // 中间的增长趋势
        ]);

      this.Tendency = Tendency; // 增长趋势
      // 赋值 左侧的图表 和 头部导航条
      this.Area_data = DATA.leftData;
      this.nav_data = DATA.NAV;
      this.midden_mapData = A.data;
      this.resource_data = LEFTBC.RESOURCE;
      this.need_data = LEFTBC.NEED;
      this.Market_data = MARKET; // 右二平台集聚
      this.enterprise_data = ENTERPRISE; // 右侧二卡片 ENTERPRISE
      this.Policy_data = POLICY.data.rows; // 右侧三 实时入驻企业
      this.CARD = [
        ...CARD,
        {
          name: "企业数量",
          // value: DATA.NAV[2].num,
          value: 10342,
          card: require("@/assets/image/chain/card5.png"),
          logo: require("@/assets/image/chain/icon5.png"),
        },
        {
          name: "政策数量",
          // value: DATA.NAV[3].num,
          value: 795,
          card: require("@/assets/image/chain/card6.png"),
          logo: require("@/assets/image/chain/icon6.png"),
        },
        {
          name: "活动数量",
          // value: DATA.NAV[4].num,
          value: 351,
          card: require("@/assets/image/chain/card7.png"),
          logo: require("@/assets/image/chain/icon7.png"),
        },
      ];
      return new Promise(function (resolve, reject) {
        resolve(true);
      });
    },
    // 根据区域名字进行请求
    async GetUserNameData(district) {
      const [DATA, Need, Supply, Firm, E, F, G, DetailsLeft] =
        await Promise.all([
          UTILS.GET_NAVDATA_FN(district), // 导航条和左1图表
          API.GET_LeftA_Port({ groupDistrict: true }), // 左2 获取需求
          API.GET_LeftB_Port({ groupDistrict: true }), // 左3 获取资源
          UTILS.GET_Firm_Fn(district), // 获取企业潜力 Firm
          UTILS.GET_PolicyData(district), // E 获取二屏政策集聚
          UTILS.GET_three_Data(district), // F获取二屏平台集聚
          UTILS.GET_four_Data(district), // G 企业潜力
          UTILS.getDetailsLeft(), // 详情左侧产品、资源数据
        ]);
      this.Area_data = DATA.leftData;
      this.Area_data = {
        Bdata: DATA.leftData.A.data, // A获取需求
        Adata: DATA.leftData.B.data, // B获取资源
      };
      // 详情左侧数据
      this.detailsList = DetailsLeft;

      this.nav_data = DATA.NAV;
      this.Details_Left_B.B = Need.data;
      this.Details_Left_B.A = Supply.data;
      this.Details_Model_one = Firm;
      this.Details_two = E; // E 获取二屏政策集聚
      this.right_three = F.arr; // F获取二屏平台集聚
      this.right_four = G; // G 企业潜力
      return new Promise(function (resolve, reject) {
        resolve(true);
      });
    },
  },
});
async function LoginFn() {
  let {
    data: { access_token },
  } = await API.Login_Port({
    username: "admin",
    smsCode: "803826",
    userType: "00",
  });
  window.sessionStorage.setItem("chain_token", access_token);
}

export default useChain;
