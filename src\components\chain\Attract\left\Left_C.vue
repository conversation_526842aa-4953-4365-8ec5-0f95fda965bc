<!-- 左三 -->
<template>
  <div class="flex flex_col flex_J_SB por list_item h32">
    <p class="NB_foot_4 item_title">企业密集度</p>
    <div style="width: 100%; height: 85%" ref="Left_C"></div>
  </div>
</template>

<script setup name="Left_C">
import * as echarts from "echarts";
import { ref, onMounted } from "vue";
let Left_C = ref(null);
var myChart_A;
onMounted(() => {
  getData();
});
function resizeHandler_A() {
  myChart_A.resize();
}
async function getData() {
  var currentIndex = 0;
  window.removeEventListener("resize", resizeHandler_A);
  window.addEventListener("resize", resizeHandler_A);
  let intervalId;
  clearInterval(intervalId);
  if (myChart_A != null && myChart_A != "" && myChart_A != undefined)
    myChart_A.dispose();
  myChart_A = echarts.init(Left_C.value);

  var option = {
    grid: {
      left: "5%", // 调整左边距以容纳左侧Y轴名称
      top: "20%", // 留出空间给图例
      bottom: "14%",
      right: "5%", // 调整右边距以容纳右侧Y轴名称
      containLabel: true, // 确保标签包含在grid内
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        textStyle: {
          color: "#fff",
        },
      },
      formatter: function (params) {
        let str = params[0].axisValue + "<br/>";
        params.forEach((item) => {
          let unit = "";
          if (
            item.seriesName === "注册企业" ||
            item.seriesName === "新增企业" ||
            item.seriesName === "规上企业"
          ) {
            unit = "家";
          } else if (item.seriesName === "工业产值") {
            unit = "万元";
          }
          str += `${item.marker} ${item.seriesName}: ${item.value} ${unit}<br/>`;
        });
        return str;
      },
    },
    legend: {
      data: ["注册企业", "新增企业", "规上企业", "工业产值"], // 更新图例数据
      orient: "horizontal",
      top: "top",
      left: "center",
      itemHeight: 12,
      itemWidth: 12,
      itemGap: 20,
      textStyle: {
        color: "white",
      },
    },
    xAxis: {
      type: "category",
      data: ["上游企业", "中游企业", "下游企业"], // 更新X轴数据
      axisLine: {
        show: true,
        lineStyle: {
          type: "dashed",
          color: "rgba(244, 244, 244,0.1)",
        },
      },
      axisTick: {
        show: false,
      },
      offset: 4,
      axisLabel: {
        interval: 0,
        textStyle: {
          color: "white",
        },
        align: "center",
      },
    },
    yAxis: [
      {
        // 左侧Y轴 - 企业数量 (家)
        type: "value",
        name: "(家)",
        nameTextStyle: {
          color: "white",
          align: "right",
        },
        min: 0,
        max: 350, // 70 * 5
        interval: 50, // 10 * 5
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          fontSize: 14,
          textStyle: {
            color: "white",
          },
        },
        splitLine: {
          show: true,
          lineStyle: { type: "dashed", color: "rgba(77, 128, 254, 0.2)" },
        },
      },
      {
        // 右侧Y轴 - 工业产值 (万元)
        type: "value",
        name: "(万元)",
        nameTextStyle: {
          color: "white",
          align: "left",
        },
        nameGap: 20,
        min: 0,
        max: 2500, // 500 * 5
        interval: 500, // 100 * 5
        position: "right",
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          fontSize: 14,
          textStyle: {
            color: "white",
          },
        },
        splitLine: {
          show: false, // 右侧Y轴不显示分割线
        },
      },
    ],
    series: [
      {
        name: "注册企业",
        type: "bar",
        yAxisIndex: 0, // 对应左侧Y轴
        data: [260, 165, 320], // 估算数据并扩大5倍
        barWidth: "15%", // 调整柱子宽度以适应多根柱子
        barGap: "20%", // 柱间距离，负值表示重叠，正值表示间隔
        barCategoryGap: "30%", // 同一类目下柱形之间的间距
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "rgb(4, 27, 68)", // 柱状图渐变色
              },
              {
                offset: 1,
                color: "#0f82f8", // 蓝色
              },
            ]),
            borderColor: "#0f82f8", // 边框颜色
            borderWidth: 1, // 边框宽度
          },
        },
        label: {
          show: false,
        },
      },
      {
        name: "新增企业",
        type: "bar",
        yAxisIndex: 0, // 对应左侧Y轴
        data: [25, 15, 35], // 估算数据并扩大5倍 (假设为小值)
        barWidth: "15%",
        barGap: "20%", // 柱间距离，负值表示重叠
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "rgba(0, 206, 209, 0.4)", // 浅蓝色渐变
              },
              {
                offset: 1,
                color: "#00CED1", // 浅蓝色
              },
            ]),
            borderColor: "#00CED1",
            borderWidth: 1,
          },
        },
        label: {
          show: false,
        },
      },
      {
        name: "规上企业",
        type: "bar",
        yAxisIndex: 0, // 对应左侧Y轴
        data: [25, 15, 25], // 估算数据并扩大5倍
        barWidth: "15%",
        barGap: "20%", // 柱间距离，负值表示重叠
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "rgba(255, 165, 0, 0.4)", // 橙色渐变
              },
              {
                offset: 1,
                color: "#FFA500", // 橙色
              },
            ]),
            borderColor: "#FFA500",
            borderWidth: 1,
          },
        },
        label: {
          show: false,
        },
      },
      {
        name: "工业产值",
        type: "line",
        yAxisIndex: 1, // 对应右侧Y轴
        data: [2000, 2250, 600], // 估算数据并扩大5倍
        smooth: true, // 平滑曲线
        symbol: "circle", // 节点为圆点
        symbolSize: 8,
        lineStyle: {
          color: "#32CD32", // 绿色线条 (LimeGreen)
          width: 2,
        },
        itemStyle: {
          color: "#32CD32", // 节点颜色
        },
        areaStyle: {
          // 区域填充
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: "rgba(50, 205, 50, 0.4)", // 区域渐变起始色 (带透明度)
            },
            {
              offset: 1,
              color: "rgba(50, 205, 50, 0)", // 区域渐变结束色 (全透明)
            },
          ]),
        },
        label: {
          show: false,
        },
      },
    ],
  };
  myChart_A.setOption(option);

  // 调整自动高亮逻辑以适配新的系列
  intervalId = setInterval(() => {
    var dataLen = option.series[0].data.length; // 使用第一个系列的数据长度
    currentIndex = (currentIndex + 1) % dataLen;

    // 遍历所有系列进行高亮和显示tooltip
    option.series.forEach((series, index) => {
      myChart_A.dispatchAction({
        type: "highlight",
        seriesIndex: index,
        dataIndex: currentIndex,
      });

      myChart_A.dispatchAction({
        type: "showTip",
        seriesIndex: index,
        dataIndex: currentIndex,
      });
    });
  }, 4000);
}
</script>

<style scoped lang="scss"></style>
