import API from "@/apis/chain/index.js";
import * as echarts from "echarts";
import CryptoJS from "crypto-js";
const secretKey = "9zVn0%bqmUYSGw2n";
import service from "@/http/chain/request";

let B_oldData = [
  {
    user: 1,
    num: GetNumber(400, 292),
  },
  {
    user: 2,
    num: GetNumber(358, 237),
  },
  {
    user: 3,
    num: GetNumber(294, 176),
  },
  {
    user: 4,
    num: GetNumber(300, 183),
  },
  {
    user: 5,
    num: GetNumber(260, 136),
  },
  {
    user: 6,
    num: GetNumber(235, 93),
  },
];
const B_need = generateRandomData(B_oldData, GetNumber(1983, 1187)).map(
  (item) => {
    return {
      name: item.user,
      value: item.num,
    };
  }
);
let B_oldresource = [
  {
    user: "国产化替代",
    num: GetNumber(832, 543),
  },
  {
    user: "管理提升",
    num: GetNumber(502, 392),
  },
  {
    user: "质量提升",
    num: GetNumber(409, 267),
  },
  {
    user: "灭菌消杀",
    num: GetNumber(883, 674),
  },
  {
    user: "新材料",
    num: GetNumber(393, 273),
  },
  {
    user: "绿色双碳",
    num: GetNumber(304, 178),
  },
];
const B_resource = generateRandomData(B_oldresource, GetNumber(5283, 4837)).map(
  (item) => {
    return {
      name: item.user,
      value: item.num,
    };
  }
);
const A_need = [
  {
    district: "黄岛",
    // value: 1024,
    value: 1772,
  },
  {
    district: "胶州",
    // value: 735,
    value: 1509,
  },
  {
    district: "城阳",
    // value: 1146,
    value: 1443,
  },
  {
    district: "即墨",
    value: 1265,
  },
  {
    district: "平度",
    // value: 876,
    value: 787,
  },
  {
    district: "莱西",
    // value: 672,
    value: 572,
  },
  {
    district: "崂山",
    // value: 1143,
    value: 511,
  },
  {
    district: "李沧",
    // value: 1082,
    value: 417,
  },
  {
    district: "市北",
    // value: 2892,
    value: 405,
  },
  {
    district: "市南",
    // value: 1368,
    value: 257,
  },
];
const A_resource = [
  {
    district: "黄岛",
    // value: 2371,
    value: 5123,
  },
  {
    district: "胶州",
    // value: 896,
    value: 4254,
  },
  {
    district: "城阳",
    // value: 1539,
    value: 4071,
  },
  {
    district: "即墨",
    value: 3557,
  },
  {
    district: "平度",
    // value: 947,
    value: 2219,
  },
  {
    district: "莱西",
    // value: 743,
    value: 1631,
  },
  {
    district: "崂山",
    // value: 2456,
    value: 1438,
  },
  {
    district: "李沧",
    // value: 1984,
    value: 1174,
  },
  {
    district: "市北",
    // value: 6839,
    value: 1132,
  },
  {
    district: "市南",
    // value: 3765,
    value: 706,
  },
];
// 模拟详情企业
const FirmTotal = GetNumber(1934, 1760);
const newFirm = generateRandomData(
  [
    { user: "智能家电", num: GetNumber(731, 637) },
    { user: "轨道交通装备", num: GetNumber(631, 537) },
    {
      user: "新能源汽车",
      num: GetNumber(543, 521),
    },
    {
      user: "高端化工",
      num: GetNumber(464, 437),
    },
    {
      user: "食品饮料",
      num: GetNumber(335, 320),
    },
    {
      user: "纺织服装",
      num: GetNumber(319, 293),
    },
    {
      user: "集成电路",
      num: GetNumber(342, 214),
    },
    {
      user: "生物医药及医疗器械",
      num: GetNumber(273, 163),
    },
  ],
  FirmTotal
);
const newFirmClass = generateRandomData(
  [
    {
      user: "注册企业",
      num: GetNumber(1873, 1389),
      url: require("@/assets/image/chain/twoA.png"),
      color: "#6ccafd",
    },
    {
      user: "高新技术企业",
      num: GetNumber(983, 683),
      url: require("@/assets/image/chain/twoB.png"),
      color: "#248899",
    },
    {
      user: "专精特新企业",
      num: GetNumber(783, 568),
      url: require("@/assets/image/chain/twoC.png"),
      color: "#25b96d",
    },
    {
      user: "创新型企业",
      num: GetNumber(793, 687),
      url: require("@/assets/image/chain/twoD.png"),
      color: "#fff758",
    },
    {
      user: "独角兽企业",
      num: GetNumber(374, 238),
      url: require("@/assets/image/chain/twoE.png"),
      color: "#ff72da",
    },
    {
      user: "瞪羚企业",
      num: GetNumber(594, 384),
      url: require("@/assets/image/chain/twoF.png"),
      color: "#40b2fa",
    },
  ],
  FirmTotal
);
//  模拟政策
const PolicyTotal = GetNumber(153, 132);

let PolicyData = generateRandomData(
  [
    { user: "A", num: GetNumber(34, 23) },
    { user: "B", num: GetNumber(43, 38) },
    { user: "C", num: GetNumber(52, 45) },
    { user: "D", num: GetNumber(24, 18) },
    { user: "E", num: GetNumber(35, 26) },
    { user: "F", num: GetNumber(42, 38) },
  ],
  PolicyTotal
);

// 工具函数
const UTILS = {
  // 增长趋势
  async GET_TendencyData() {
    const [A, B] = await Promise.all([
      service({
        url: `system/demand/getLastTwelveMonthTotalNum`,
        method: "GET",
      }), // 需求
      service({
        url: `system/supply/getLastTwelveMonthTotalNum`,
        method: "GET",
      }), // 供给
    ]);
    return {
      A: A.data,
      B: B.data,
    };
  },
  // 中间的卡片
  async GET_CardData() {
    const [A, B, C, D] = await Promise.all([
      service({
        url: `system/expert/listTen?pageNum=1&pageSize=1`,
        method: "GET",
      }), // 专家
      service({
        url: `system/web/tyInstrument/list?pageNum=1&pageSize=1`,
        method: "GET",
      }), // 设备
      // service({
      //   url: `system/demand/gatewayListShow?displayStatus=1&auditStatus=2&pageNum=1&pageSize=1`,
      //   method: "GET",
      // }), // 需求
      // service({
      //   url: `system/supply/gatewayListTen?auditStatus=2&displayStatus=1&pageNum=1`,
      //   method: "GET",
      // }), // 资源
    ]);

    return [
      {
        name: "需求数量",
        value: 9053,
        // value: decode_Fn(C).total,
        card: require("@/assets/image/chain/card1.png"),
        logo: require("@/assets/image/chain/icon1.png"),
      },
      {
        name: "资源数量",
        value: 25382,
        // value: decode_Fn(D).total,
        card: require("@/assets/image/chain/card2.png"),
        logo: require("@/assets/image/chain/icon2.png"),
      },
      {
        name: "专家数量",
        // value: decode_Fn(A).total,
        value: 11989,
        card: require("@/assets/image/chain/card3.png"),
        logo: require("@/assets/image/chain/icon3.png"),
      },
      {
        name: "设备数量",
        // value: B.total,
        value: 1441,
        card: require("@/assets/image/chain/card4.png"),
        logo: require("@/assets/image/chain/icon4.png"),
      },
    ];
  },
  getDetailsLeft() {
    return {
      demand: A_need,
      product: A_resource,
    };
  },
  // 导航和左侧柱状图
  async GET_NAVDATA_FN(district) {
    let obj = district
      ? {
          groupName: true,
          district,
        }
      : { groupDistrict: true };

    const [A, B, C, D, E] = await Promise.all([
      // API.GET_LeftA_Port(obj), // A获取需求
      // API.GET_LeftB_Port(obj), // B获取资源
      {
        data: district ? B_need : A_need,
      },
      {
        data: district ? B_resource : A_resource,
      },
      API.GET_NavC_Port({
        recommendStatus: 1,
        pageNum: 1,
        ...(district ? { district } : {}),
      }), // nav C链企业
      API.GET_NavD_Port({
        kind: 0,
        pageNum: 1,
        pageSize: 1,
        ...(district ? { district } : {}),
      }), // nav  D链政策
      API.GET_NavE_Port({
        pageNum: 1,
        pageSize: 1,
        ...(district ? { district } : {}),
      }), // nav E链活动
    ]);
    const Atotal = A.data.reduce((sum, item) => sum + item.value, 0);
    const Btotal = B.data.reduce((sum, item) => sum + item.value, 0);
    // 根据传参筛选出导航的数据
    // 需求  A_need
    // 产品  A_resource
    // 企业  getNumByDistrict()
    let NewA_need = A_need.find((item) => item.district === district)?.value;
    let NewA_resource = A_resource.find(
      (item) => item.district === district
    )?.value;
    let New_enterprise = this.getNumByDistrict().data.find((item) =>
      item.name.includes(district)
    )?.value;
    return {
      NAV: [
        // {
        //   user: "链需求",
        //   num: Atotal,
        //   class: "icon-zhaoxuqiu",
        // },
        // {
        //   user: "链资源",
        //   num: Btotal,
        //   class: "icon-ziyuan",
        // },
        // {
        //   user: "链企业",
        //   num: district ? FirmTotal : decode_Fn(C).total,
        //   class: "icon-qiye",
        // },
        {
          user: "链需求",
          num: NewA_need,
          class: "icon-zhaoxuqiu",
        },

        {
          user: "链资源",
          num: NewA_resource,
          class: "icon-ziyuan",
        },

        {
          user: "链企业",
          num: New_enterprise,
          class: "icon-qiye",
        },
        {
          user: "链政策",
          num: district ? PolicyTotal : D.total,
          class: "icon-classification",
        },
        {
          user: "链活动",
          num: district ? GetNumber(71, 58) : E.total,
          class: "icon-huodongguanli",
        },
      ],
      leftData: {
        A: {
          sum: Atotal,
          data: A.data,
        },
        B: {
          sum: Btotal,
          data: B.data,
        },
      },
    };
  },
  // 获取地图企业数量
  getNumByDistrict() {
    return {
      data: [
        { name: "黄岛区", value: "1774" },
        { name: "胶州市", value: "1512" },
        { name: "城阳区", value: "1446" },
        { name: "即墨区", value: "1267" },
        { name: "平度市", value: "788" },
        { name: "莱西市", value: "573" },
        { name: "崂山区", value: "511" },
        { name: "李沧区", value: "418" },
        { name: "市北区", value: "406" },
        { name: "市南区", value: "251" },
      ],
    };
  },
  // 左侧bc柱子
  async GET_LEFTBC_Fn() {
    const [A, B] = await Promise.all([
      API.GET_LeftA_Port({
        groupName: true,
      }), // 获取需求
      API.GET_LeftB_Port({
        groupName: true,
      }), // 获取资源
    ]);
    // console.log("GET_LEFTBC_Fn ~ A, B:", A, B);
    const NAME = {
      1: "创新研发",
      2: "物料采购",
      3: "智能制造",
      4: "数字化管理",
      5: "软件服务",
      6: "供应链金融",
      8: "其他",
    };
    return {
      NEED: [
        {
          name: NAME[A.data[0].name],
          value: A.data[0].value,
        },
        {
          name: NAME[A.data[1].name],
          value: A.data[1].value,
        },
        {
          name: NAME[A.data[2].name],
          value: A.data[2].value,
        },
        {
          name: NAME[A.data[3].name],
          value: A.data[3].value,
        },
        {
          name: NAME[A.data[4].name],
          value: A.data[4].value,
        },
        {
          name: NAME[A.data[5].name],
          value: A.data[5].value,
        },
      ].sort((a, b) => b.value - a.value),
      RESOURCE: [
        {
          name: B.data[0].name,
          value: B.data[0].value,
          itemStyle: {
            normal: {
              color: colorFn("#5ff6ff", "#318de5"),
              barBorderRadius: [4, 4, 0, 0],
            },
          },
        },
        {
          name: B.data[1].name,
          value: B.data[1].value,
          itemStyle: {
            normal: {
              color: colorFn("#9b3ed8", "#331346"),
              barBorderRadius: [4, 4, 0, 0],
            },
          },
        },
        {
          name: B.data[2].name,
          value: B.data[2].value,
          itemStyle: {
            normal: {
              color: colorFn("#ff9806", "#eedb66"),
              barBorderRadius: [4, 4, 0, 0],
            },
          },
        },
        {
          name: B.data[3].name,
          value: B.data[3].value,
          itemStyle: {
            normal: {
              color: colorFn("#5051e3", "#318de5"),
              barBorderRadius: [4, 4, 0, 0],
            },
          },
        },
        {
          name: B.data[4].name,
          value: B.data[4].value,
          itemStyle: {
            normal: {
              color: colorFn("#cb4984", "#bf96bf"),
              barBorderRadius: [4, 4, 0, 0],
            },
          },
        },
        {
          name: B.data[5].name,
          value: B.data[5].value,
          itemStyle: {
            normal: {
              color: colorFn("#74d500", "#40a4a0"),
              barBorderRadius: [4, 4, 0, 0],
            },
          },
        },
      ].sort((a, b) => b.value - a.value),
    };
  },
  // 右侧的企业潜力
  async GET_Enterprise_Fn() {
    let [A, B, C, D, E, F] = await Promise.all([
      API.GET_Enterprise_Port({
        recommendStatus: 1,
        pageNum: 1,
        pageSize: 1,
      }), // 全部企业
      // API.GET_Enterprise_Port({
      //   recommendStatus: 1,
      //   pageNum: 1,
      //   pageSize: 1,
      //   companyLabel: "高新技术企业",
      // }), // 高新技术企业
      // API.GET_Enterprise_Port({
      //   recommendStatus: 1,
      //   pageNum: 1,
      //   pageSize: 1,
      //   companyLabel: "专精特新企业",
      // }), // 专精特新企业
      // API.GET_Enterprise_Port({
      //   recommendStatus: 1,
      //   pageNum: 1,
      //   pageSize: 1,
      //   companyLabel: "创新型企业",
      // }), // 创新型企业
      // API.GET_Enterprise_Port({
      //   recommendStatus: 1,
      //   pageNum: 1,
      //   pageSize: 1,
      //   companyLabel: "独角兽企业",
      // }), // 独角兽企业
      // API.GET_Enterprise_Port({
      //   recommendStatus: 1,
      //   pageNum: 1,
      //   pageSize: 1,
      //   companyLabel: "瞪羚企业",
      // }), // 瞪羚企业
    ]);
    return [
      { key: "注册企业", value: decode_Fn(A).total, color: "#6fe0db" },
      {
        key: "高新技术企业",
        //  value: decode_Fn(B).total,
        value: 139,
        color: "#3e7cd8",
      },
      {
        key: "专精特新企业",
        value: 125,
        // value: decode_Fn(C).total,
        color: "#74d597",
      },
      {
        key: "创新型企业",
        value: 89,
        // value: decode_Fn(D).total,
        color: "#eedb66",
      },
      {
        key: "独角兽企业",
        value: 62,
        // value: decode_Fn(E).total,
        color: "#5051e3",
      },
      {
        key: "瞪羚企业",
        // value: decode_Fn(F).total,
        value: 48,
        color: "#cb4984",
      },
    ];
  },
  // 右二平台集聚
  async GET_Market_Fn() {
    let [A, B, C] = await Promise.all([
      service({
        url: `system/web/wq_plat/list?type=0&pageNum=1&pageSize=1`,
        method: "GET",
      }), // 平台集聚 政府
      service({
        url: `system/web/wq_plat/list?type=1&pageNum=1&pageSize=1`,
        method: "GET",
      }), // 平台集聚 行业
      service({
        url: `system/web/wq_plat/list?type=2&pageNum=1&pageSize=1`,
        method: "GET",
      }), // 平台集聚 企业
    ]);
    return [
      {
        key: "政府平台",
        value: A.total,
        url: require("@/assets/image/chain/chain_item1.png"),
      },
      {
        key: "行业平台",
        value: B.total,
        url: require("@/assets/image/chain/chain_item5.png"),
      },
      {
        key: "企业平台",
        value: C.total,
        url: require("@/assets/image/chain/chain_item6.png"),
      },
    ];
  },
  // 右侧三 获取政策
  async GET_Policy_Fn() {
    let [A, B, C, D, E] = await Promise.all([
      service({
        url: `system/information/listByText?text=人才&kind=0&pageNum=1&pageSize=1`,
        method: "GET",
      }), // 人才
      service({
        url: `system/information/listByText?text=融资&kind=0&pageNum=1&pageSize=1`,
        method: "GET",
      }), // 融资
      service({
        url: `system/information/listByText?text=创新平台&kind=0&pageNum=1&pageSize=1`,
        method: "GET",
      }), // 创新平台
      service({
        url: `system/information/listByText?text=先进制造业&kind=0&pageNum=1&pageSize=1`,
        method: "GET",
      }), // 先进制造业
      service({
        url: `system/information/listByText?text=创新&kind=0&pageNum=1&pageSize=1`,
        method: "GET",
      }), // 创新
    ]);
    return [
      { name: "人才", value: A.total },
      { name: "融资", value: B.total },
      { name: "创新平台", value: C.total },
      { name: "先进制造业", value: D.total },
      { name: "创新", value: E.total },
    ];
  },
  // 获取左1图表数据
  async GET_Area_Fn(text) {
    let [A, B] = await Promise.all([
      service({
        url: `system/supply/getNumByDistrict?district=${text}`,
        method: "GET",
      }), // 供给
      service({
        url: `system/demand/getNumByDistrict?district=${text}`,
        method: "GET",
      }), // 需求
    ]);
    if (text) {
      // 详情需要 // 资源总数
      const AMax = A.data.reduce((max, item) => {
        return Math.max(max, parseInt(item.value, 10));
      }, -Infinity);
      A.data.forEach((item) => {
        const percentage = ((parseInt(item.value, 10) / AMax) * 100).toFixed(2); // 计算百分比并保留两位小数
        item.num = `${percentage}%`; // 转换为字符串并添加%
      });

      // 需求总数
      const BMax = B.data.reduce((max, item) => {
        return Math.max(max, parseInt(item.value, 10));
      }, -Infinity);
      B.data.forEach((item) => {
        const percentage = ((parseInt(item.value, 10) / BMax) * 100).toFixed(2); // 计算百分比并保留两位小数
        item.num = `${percentage}%`; // 转换为字符串并添加%
      });

      return {
        A: A.data,
        B: B.data,
      };
    } else {
      console.log(A, B);
      // 首页的
      return {
        A: {
          sum: A.data.reduce((num, item) => (num += item.value - 0), 0),
          data: A.data,
        },
        B: {
          sum: B.data.reduce((num, item) => (num += item.value - 0), 0),
          data: B.data,
        },
      };
    }
  },
  // 获取企业潜力
  async GET_Firm_Fn(text) {
    // let [A, B, C, D, E, F, G, H] = await Promise.all([
    //   service({
    //     url: `system/company/mag/secret/getCompanylist_cxhl_list?industrialChain=1&recommendStatus=1&pageNum=1&district=${text}`,
    //     method: "GET",
    //   }), // A智能家电
    //   service({
    //     url: `system/company/mag/secret/getCompanylist_cxhl_list?industrialChain=2&recommendStatus=1&pageNum=1&district=${text}`,
    //     method: "GET",
    //   }), // B轨道交通装备
    //   service({
    //     url: `system/company/mag/secret/getCompanylist_cxhl_list?industrialChain=3&recommendStatus=1&pageNum=1&district=${text}`,
    //     method: "GET",
    //   }), // C新能源汽车
    //   service({
    //     url: `system/company/mag/secret/getCompanylist_cxhl_list?industrialChain=4&recommendStatus=1&pageNum=1&district=${text}`,
    //     method: "GET",
    //   }), // D高端化工
    //   service({
    //     url: `system/company/mag/secret/getCompanylist_cxhl_list?industrialChain=6&recommendStatus=1&pageNum=1&district=${text}`,
    //     method: "GET",
    //   }), // E食品饮料
    //   service({
    //     url: `system/company/mag/secret/getCompanylist_cxhl_list?industrialChain=7&recommendStatus=1&pageNum=1&district=${text}`,
    //     method: "GET",
    //   }), // F纺织服装
    //   service({
    //     url: `system/company/mag/secret/getCompanylist_cxhl_list?industrialChain=8&recommendStatus=1&pageNum=1&district=${text}`,
    //     method: "GET",
    //   }), // G集成电路
    //   service({
    //     url: `system/company/mag/secret/getCompanylist_cxhl_list?industrialChain=12&recommendStatus=1&pageNum=1&district=${text}`,
    //     method: "GET",
    //   }), // H集成电路
    // ]);
    // return [
    //   { key: "智能家电", value: decode_Fn(A).total, color: "#6fe0db" },
    //   { key: "轨道交通装备", value: decode_Fn(B).total, color: "#3e7cd8" },
    //   {
    //     key: "新能源汽车",
    //     value: decode_Fn(C).total,
    //     color: "#74d597",
    //   },
    //   {
    //     key: "高端化工",
    //     value: decode_Fn(D).total,
    //     color: "#eedb66",
    //   },
    //   {
    //     key: "食品饮料",
    //     value: decode_Fn(E).total,
    //     color: "#5051e3",
    //   },
    //   {
    //     key: "纺织服装",
    //     value: decode_Fn(F).total,
    //     color: "#cb4984",
    //   },
    //   {
    //     key: "集成电路",
    //     value: decode_Fn(G).total,
    //     color: "#cb4984",
    //   },
    //   {
    //     key: "生物医药及医疗器械",
    //     value: decode_Fn(H).total,
    //     color: "#cb4984",
    //   },
    // ];
    return newFirm.map((item) => {
      return {
        key: item.user,
        value: item.num,
      };
    });
  },
  // 获取二屏政策集聚
  async GET_PolicyData(district) {
    let obj = {
      text: "",
      releaseId: "",
      policyStatus: "",
      labelCodeList: [],
      district,
    };
    let data = {
      A: {
        ...obj,
        type: "1",
      },
      B: {
        ...obj,
        type: "2",
      },
      C: {
        ...obj,
        type: "3",
      },
      D: {
        ...obj,
        type: "4",
      },
      E: {
        ...obj,
        type: "100",
      },
      F: {
        ...obj,
        type: "101",
      },
    };
    // const [A, B, C, D, E, F] = await Promise.all([
    //   API.POST_PolicyList_Port(data["A"]), // 创新政策
    //   API.POST_PolicyList_Port(data["B"]), // 创业政策
    //   API.POST_PolicyList_Port(data["C"]), // 融资政策
    //   API.POST_PolicyList_Port(data["D"]), // 先进制造业政策
    //   API.POST_PolicyList_Port(data["E"]), // 科创平台
    //   API.POST_PolicyList_Port(data["F"]), // 人才政策
    // ]);

    // return {
    //   A: A.total,
    //   B: B.total,
    //   C: C.total,
    //   D: D.total,
    //   E: E.total,
    //   F: F.total,
    // };
    return PolicyData.reduce((acc, item) => {
      acc[item.user] = item.num;
      return acc;
    }, {});
  },
  // 获取二屏平台集聚
  async GET_three_Data(district) {
    const DATA = {
      A: { type: 0, pageNum: 1, pageSize: 1, district },
      B: { type: 1, pageNum: 1, pageSize: 1, district },
      C: { type: 2, pageNum: 1, pageSize: 1, district },
      D: { pageNum: 1, pageSize: 10, district },
    };
    const [A, B, C, D] = await Promise.all([
      API.GET_Platform_Port(DATA["A"]), // 政府
      API.GET_Platform_Port(DATA["B"]), // 行业
      API.GET_Platform_Port(DATA["C"]), // 企业
      API.GET_Platform_Port(DATA["D"]), // 相关推荐
    ]);
    // console.log("GET_three_Data ~ D:", A, B, C);
    // console.log("GET_three_Data ~ D:", D.rows);

    return {
      arr: [
        { name: "政府平台", value: A.total },
        { name: "行业平台", value: B.total },
        { name: "企业平台", value: C.total },
      ],
      rows: D.rows,
    };
  },
  // 获取二屏企业潜力
  async GET_four_Data(district) {
    let arr = [
      {
        user: "注册企业",
        num: GetNumber(1873, 1389),
        url: require("@/assets/image/chain/twoA.png"),
        color: "#6ccafd",
      },
      {
        user: "高新技术企业",
        num: GetNumber(983, 683),
        url: require("@/assets/image/chain/twoB.png"),
        color: "#248899",
      },
      {
        user: "专精特新企业",
        num: GetNumber(783, 568),
        url: require("@/assets/image/chain/twoC.png"),
        color: "#25b96d",
      },
      {
        user: "创新型企业",
        num: GetNumber(793, 687),
        url: require("@/assets/image/chain/twoD.png"),
        color: "#fff758",
      },
      {
        user: "独角兽企业",
        num: GetNumber(374, 238),
        url: require("@/assets/image/chain/twoE.png"),
        color: "#ff72da",
      },
      {
        user: "瞪羚企业",
        num: GetNumber(594, 384),
        url: require("@/assets/image/chain/twoF.png"),
        color: "#40b2fa",
      },
    ];
    return newFirmClass.map((item, i) => {
      return {
        name: item.user,
        value: item.num,
        url: arr[i].url,
        color: arr[i].color,
      };
    });
    // const DATA = {
    //   2: { companyLabel: "高新技术企业" },
    //   3: { companyLabel: "专精特新企业" },
    //   4: { companyLabel: "创新型企业" },
    //   5: { companyLabel: "独角兽企业" },
    //   6: { companyLabel: "瞪羚企业" },
    // };
    // const PARAMS = {
    //   recommendStatus: 1,
    //   pageNum: 1,
    //   pageSize: 1,
    //   district,
    // };
    // const [A, B, C, D, E, F] = await Promise.all([
    //   API.GET_Enterprise_Port(PARAMS), //注册企业A
    //   API.GET_Enterprise_Port({
    //     ...PARAMS,
    //     ...DATA[2],
    //   }), //高新技术企业 B
    //   API.GET_Enterprise_Port({
    //     ...PARAMS,
    //     ...DATA[3],
    //   }), //专精特新企业C
    //   API.GET_Enterprise_Port({
    //     ...PARAMS,
    //     ...DATA[4],
    //   }), //创新型企业D
    //   API.GET_Enterprise_Port({
    //     ...PARAMS,
    //     ...DATA[5],
    //   }), //独角兽企业E
    //   API.GET_Enterprise_Port({
    //     ...PARAMS,
    //     ...DATA[6],
    //   }), //瞪羚企业F
    // ]);
    // // GetNumber(1983, 1187)
    // return [
    //   {
    //     name: "注册企业",
    //     value: decode_Fn(A).total,
    //     url: require("@/assets/image/chain/twoA.png"),
    //     color: "#6ccafd",
    //   },
    //   {
    //     name: "高新技术企业",
    //     value: decode_Fn(B).total,
    //     url: require("@/assets/image/chain/twoB.png"),
    //     color: "#248899",
    //   },
    //   {
    //     name: "专精特新企业",
    //     value: decode_Fn(C).total,
    //     url: require("@/assets/image/chain/twoC.png"),
    //     color: "#25b96d",
    //   },
    //   {
    //     name: "创新型企业",
    //     value: decode_Fn(D).total,
    //     url: require("@/assets/image/chain/twoD.png"),
    //     color: "#fff758",
    //   },
    //   {
    //     name: "独角兽企业",
    //     value: decode_Fn(E).total,
    //     url: require("@/assets/image/chain/twoE.png"),
    //     color: "#ff72da",
    //   },
    //   {
    //     name: "瞪羚企业",
    //     value: decode_Fn(F).total,
    //     url: require("@/assets/image/chain/twoF.png"),
    //     color: "#40b2fa",
    //   },
    // ];
  },
};

// 提供解密
function decode_Fn(data) {
  let key = CryptoJS.enc.Utf8.parse(secretKey);
  let decrypt = CryptoJS.AES.decrypt(data, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  });
  return JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));
}
function colorFn(A, B) {
  return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    {
      offset: 0,
      color: A,
    },
    {
      offset: 1,
      color: B,
    },
  ]);
}
// 模拟数据
function GetNumber(max, min) {
  return Math.floor(Math.random() * (max - min + 1) + min);
} // 生成随机数据的函数
function generateRandomData(data, total) {
  // 计算每类数据的百分比
  const percentages = data.map((item) => ({
    user: item.user,
    percentage: item.num / data.reduce((sum, i) => sum + i.num, 0),
  }));

  // 生成初步随机数据
  let randomData = percentages.map((item) => {
    const estimatedNum = Math.floor(item.percentage * total);
    return {
      user: item.user,
      num: estimatedNum,
    };
  });
  // console.log("randomData ~ randomData:", randomData);

  // 计算初步随机数据的总和
  let currentTotal = randomData.reduce((sum, item) => sum + item.num, 0);

  // 调整总和以匹配目标总数
  const diff = total - currentTotal;

  if (diff !== 0) {
    // 简单起见，均匀分配差额（可以使用更复杂的调整策略）
    const adjustmentPerItem = Math.floor(diff / randomData.length);

    randomData = randomData.map((item) => ({
      user: item.user,
      num: item.num + adjustmentPerItem,
    }));

    // 重新计算总和
    currentTotal = randomData.reduce((sum, item) => sum + item.num, 0);
    const finalDiff = total - currentTotal;

    // 对差额进行简单调整（可以改进为更复杂的算法）
    if (finalDiff !== 0) {
      // 对第一个类别进行调整
      randomData[0].num += finalDiff;
    }
  }

  return randomData;
}

export default UTILS;
