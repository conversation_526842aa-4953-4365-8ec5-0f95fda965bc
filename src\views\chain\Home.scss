.Home {
  background: url("@/assets/image/chain/bgc.png") no-repeat center center;
  background-size: 100% 100%;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
  .box_item {
    height: 25vmin;
  }
  .main_item {
    width: 29%;
    min-width: 29%;
    // width: 29vmax;
    // min-width: 29vmax;
    position: relative;
  }
  .Homebox {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    nav {
      position: relative;
      width: 100%;
      margin-bottom: 1.8vmin;
      h1 {
        position: absolute;
        left: 50%;
        top: 50%;
        z-index: 99;
        transform: translate(-50%, -56%) skew(-6deg);
        font-size: 3.4vmin;
        letter-spacing: 0.3vmin;
      }
      img {
        width: 100%;
      }
      .btnBtn {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0%;
        transform: translateY(-50%);
        top: 50%;
        .activeBtn {
          filter: brightness(1.8);
          z-index: 66;
        }
        .btn {
          width: 8vw;
          height: 5vh;
          position: absolute;
          color: #fff;
          cursor: pointer;
          text-align: center;
          line-height: 5vh;
          font-size: 0.8vw;
          font-weight: 400;
          transition: all 0.2s ease-in;
          &:hover {
            filter: brightness(1.5);
            z-index: 66;
          }
        }
        .position1 {
          bottom: 22%;
          left: 4%;
        }
        .position2 {
          bottom: 22%;
          left: 12%;
        }

        .position3 {
          bottom: 22%;
          right: 12%;
        }

        .position4 {
          bottom: 22%;
          right: 4%;
        }
        .btn-left {
          background: url("@/assets/image/btn_left.png") no-repeat;
          background-size: 100% 100%;
        }

        .btn-right {
          background: url("@/assets/image/btn_right.png") no-repeat;
          background-size: 100% 100%;
        }
      }
    }
    main {
      flex: 1;
      height: 70%;
      margin: 0 1vmin 1.2vmin;
      .headerBox {
        margin-bottom: 1.8vmin;
        border-radius: 16px;
        border: 1.6px solid #213e8d;
        padding: 1vmin 0;
        box-shadow: inset 0 0 14px #102d7a, inset 0 0 40px #032c7f;
        li {
          width: 20%;
          position: relative;
          &:nth-child(-n + 4)::after {
            content: "";
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            height: 100%;
            width: 0.5%;
            border-radius: 50%;
            background: rgb(125, 177, 228);
            background: radial-gradient(
              circle,
              rgb(142, 198, 255) 60%,
              rgb(66, 102, 169)
            );
          }
          .header_l {
            position: relative;
            .header_span {
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-47%, -70%);
              font-size: 2.6vmin;
              color: #edefec !important;
            }
          }
        }
      }
    }
  }
}
.list_itemMax {
  background: url("@/assets/image/chain/itemMax.png") no-repeat center center !important;
  background-size: 100% 100% !important;
}
.list_itemPro {
  background: url("@/assets/image/chain/itemPro.png") no-repeat center center !important;
  background-size: 100% 100% !important;
}
.list_item {
  padding: 0.5vmin 2vmin 2vmin;
  background: url("@/assets/image/chain/item.png") no-repeat center center;
  background-size: 100% 100%;
  &:hover {
    .item_title::after {
      width: 100%;
    }
  }
  .item_title {
    position: relative;
    padding-bottom: 1%;
    font-size: 1.8vmin;
    white-space: nowrap;
    &:before {
      content: "";
      position: absolute;
      background-image: linear-gradient(to right, #e6f4f8, #66ddf8, #1e7ef8);
      left: 0%;
      bottom: 0%;
      width: 100%;
      height: 2px;
      border-radius: 0 50% 20px 0;
    }
    &::after {
      content: "";
      will-change: width;
      width: 10%;
      height: 3px;
      position: absolute;
      left: 0%;
      bottom: -1.5%;
      background-color: #4cadff;
      transition: all 0.6s ease-in-out;
    }
  }
}
@keyframes section {
  0% {
    -webkit-transform: rotateX(-90deg);
    transform: rotateX(-90deg);
  }
  50%,
  75% {
    -webkit-transform: rotateX(0);
    transform: rotateX(0);
  }
  100% {
    opacity: 0;
    -webkit-transform: rotateX(0);
    transform: rotateX(0);
  }
}

section {
  h5 {
    margin-top: 6%;
  }
  .loading {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    height: 60px;
    width: 60px;
    div {
      animation: section 1.5s calc(var(--delay) * 1s) infinite backwards;
      background-color: #fff;
    }
    div:nth-child(1) {
      --delay: 0.1;
    }
    div:nth-child(2) {
      --delay: 0.02;
    }
    div:nth-child(3) {
      --delay: 0.12;
    }
    div:nth-child(4) {
      --delay: 0.02;
    }
    div:nth-child(5) {
      --delay: 0.02;
    }
    div:nth-child(6) {
      --delay: 0.12;
    }
    div:nth-child(7) {
      --delay: 0.18;
    }
    div:nth-child(8) {
      --delay: 0.16;
    }
    div:nth-child(9) {
      --delay: 0.14;
    }
  }
}
.map_tooltip {
  min-width: 100px;
  height: auto;
  // min-height: 70px;
  background: url("@/assets/image/chain/map.png") no-repeat center center;
  background-size: 100% 100%;
  border: none;
  padding: 2vmin;
  .map_text {
    padding: 0.7vmin 0;
  }
  .map_name {
    font-weight: 900;
    font-size: 1.75vmin;
    color: #279cff;
  }
}

.Left_Three {
  padding-top: 3.4vmin;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .li_taps {
    .actveIndex {
      color: #fccb3a;
      p {
        background-color: #fccb3a;
        box-shadow: 0 0 2vmin #fccb3a;
      }
    }
    .span_i {
      position: absolute;
      top: -38%;
      color: #4599f4;
    }
    .span_il {
      left: -2%;
    }
    .span_ir {
      transform: rotate(180deg);
      right: -2%;
    }
    p {
      border-radius: 50%;
      width: 1vmin;
      height: 1vmin;
      background-color: #4599f4;
      box-shadow: 0 0 2vmin #4599f4;
    }
  }
  .li_taps::before {
    position: absolute;
    content: "";
    top: 0;
    width: 100%;
    height: 2px;
    background-color: #3595e7;
    z-index: -1;
  }
  .li_item {
    min-width: 100%;
    // padding: 0 10px 0 0;
    height: 100%;
  }
  li {
    height: 2vmin;
    min-height: 10%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .progress_name {
      padding-right: 2%;
      font-size: 2vmin;
    }
    .progress_num {
      padding-left: 2%;
      text-align: right;
    }
    // p {
    //   width: 12%;
    // }
    .progress {
      flex: 1;
      height: 100%;
      @keyframes progressAnimation {
        0% {
          background-position: 0 0;
        }
        100% {
          background-position: 28px 0;
        }
      }
    }
  }
}
.Left_A {
  padding: 1.1vmin 0 0;
  .right {
    width: 60%;
    & > div {
      width: 33%;
      .num {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        color: #c8fafe;
        font-weight: 900;
        text-shadow: 0 0 1px #c8fafe;
      }
      span {
        text-align: center;
        padding-top: 0.45vmin;
      }
      img {
        width: 68%;
      }
    }
  }
  .left {
    width: 32%;
    img {
      width: 100%;
    }
  }
}
