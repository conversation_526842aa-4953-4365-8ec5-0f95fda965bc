<template>
  <div class="flex1 MapModule por">
    <div class="mapChoose">
      <span v-for="(item, index) in parentInfo" :key="item.code">
        <button class="button type1 fs14" @click="chooseArea(item, index)">
          {{ item.cityName == "全国" ? "中国" : item.cityName }}
        </button>
      </span>
    </div>
    <div class="chatbox" style="width: 100%; height: 100%" ref="Left_C"></div>
    <div class="New_MapCard">
      <!-- 确保 v-for 循环中包含 index -->
      <div v-for="(item, index) in items" :key="index" class="MapCard_item">
        <div class="item_text w100 h100 flex flex_A_C">
          <div class="cara_logo flex_A_C h100 flex flex_J_C">
            <!-- 使用 (index % 7) + 1 来实现图片索引的循环 -->
            <img
              class=""
              :src="
                require('@/assets/image/chain/icon' +
                  ((index % 7) + 1) +
                  '.png')
              "
              alt=""
            />
          </div>
          <span class="NB_foot_7 cara_span">
            {{ item.name }}
          </span>
          <span class="NB_foot_3">{{ item.value }}</span>
        </div>
        <!-- 使用 (index % 7) + 1 来实现图片索引的循环 -->
        <img
          class="card_box"
          :src="
            require('@/assets/image/chain/card' + ((index % 7) + 1) + '.png')
          "
          alt=""
        />
      </div>
    </div>
  </div>
</template>

<script setup name="New_Map">
import "echarts-gl";
import * as echarts from "echarts";
import { onMounted, ref, nextTick } from "vue";
import DATA from "./data.js";
import useCounter from "@/stores/chain/chain.js";
const stores = useCounter();
let Left_C = ref(null);
let mapData = [];
let geoJson = {
  features: [],
};
let items = ref([]);
var myChart = null;
let parentInfo = ref([
  {
    //地图列表（默认为全国-中国）
    cityName: "吉安市", //
    code: 360800, //全国的区划编码
  },
]);

onMounted(() => {
  nextTick(() => {
    items.value = DATA[0].items;
    getGeoJson(360800); // 将行政区划代码更改为吉安市的 360700
    window.addEventListener("resize", () => {
      myChart.resize();
    });
  });
});
function chooseArea(val, index) {
  console.log("🚀 ~ chooseArea ~ index:", index);
  if (index === 0) items.value = DATA[0].items;

  if (parentInfo.value.length === index + 1) {
    return;
  }
  parentInfo.value.splice(index + 1);
  getGeoJson(parentInfo.value[parentInfo.value.length - 1].code);
}
const getGeoJson = (adcode, data) => {
  AMapUI.loadUI(["geo/DistrictExplorer"], (DistrictExplorer) => {
    var districtExplorer = new DistrictExplorer();
    districtExplorer.loadAreaNode(adcode, function (error, areaNode) {
      if (error) return console.error(error);
      let Json = areaNode.getSubFeatures();
      if (Json.length > 0) {
        geoJson.features = Json;
      } else if (Json.length === 0) {
        // 不存在子级区域时，在原数组中筛选自己本身
        geoJson.features = geoJson.features.filter(
          (item) => item.properties.adcode == adcode
        );
        if (geoJson.features.length === 0) return;
      }
      // 地图数据
      mapData = geoJson.features.map((item) => {
        return {
          name: item.properties.name,
          value: Math.random() * 5,
          cityCode: item.properties.adcode,
        };
      });
      mapData = mapData.sort(function (a, b) {
        return b.value - a.value;
      });
      // 左上角标签
      if (data) {
        parentInfo.value.push({
          cityName: data.name,
          code: data.cityCode,
        });
      }
      initEmyCharts(true);
    });
  });
};
function seriesFn() {
  const maxValue = mapData.reduce((max, item) => {
    return Math.max(max, parseInt(item.value, 10));
  }, -Infinity);
  const minValue = mapData.reduce((min, item) => {
    return Math.min(min, parseInt(item.value, 10));
  }, Infinity);
  let series = [
    {
      viewControl: {
        distance: 160,
        alpha: 46,
        animation: true,
        // center: [-10, -26, 0],
        center: [-24, -20, 0],
      },
      name: "map3D地图分布",
      type: "map3D", //地图
      map: "china", //使用 registerMap 注册的地图名称 ,必须和 registerMap 注册的一致
      label: {
        normal: {
          position: "bottom",
          color: "#fff",
          fontSize: 12,
          show: true,
        },
      },
      itemStyle: {
        areaColor: "rgba(0,81,138,0)", //底色
        borderColor: "#4feef3", //边框
        borderWidth: 1.2,
        shadowBlur: 6,
        shadowColor: "#ccf9ff",
        emphasis: {
          areaColor: "red", // 鼠标悬停时的背景色
          // 可以在这里设置鼠标悬停时的label样式
          label: {
            textStyle: {
              color: "white", // 鼠标悬停时的文字颜色
            },
          },
        },
      },
      emphasis: {
        //高亮状态下的多边形和标签样式
        label: {
          show: true,
          color: "#bcaf22",
        },
        itemStyle: {
          color: "#bcaf22",
          opacity: 1,
        },
      },
      light: {
        // 光照相关的设置。在 shading 为 'color' 的时候无效。  光照的设置会影响到组件以及组件所在坐标系上的所有图表。合理的光照设置能够让整个场景的明暗变得更丰富，更有层次。
        main: {
          // 场景主光源的设置，在 globe 组件中就是太阳光。
          color: "#fff", // 主光源的颜色。[ default: #fff ]
          intensity: 1, // 主光源的强度。[ default: 1 ]
          shadow: true, // 主光源是否投射阴影。默认关闭。    开启阴影可以给场景带来更真实和有层次的光照效果。但是同时也会增加程序的运行开销。
          // shadowQuality: 'medium',      // 阴影的质量。可选'low', 'medium', 'high', 'ultra' [ default: 'medium' ]
          alpha: 55, // 主光源绕 x 轴，即上下旋转的角度。配合 beta 控制光源的方向。[ default: 40 ]
          beta: -0, // 主光源绕 y 轴，即左右旋转的角度。[ default: 40 ]
        },
        ambient: {
          // 全局的环境光设置。
          color: "#fff", // 环境光的颜色。[ default: #fff ]
          intensity: 0.2, // 环境光的强度。[ default: 0.2 ]
        },
      },
      data: mapData,
    },
  ];
  // 设置图表实例的配置项以及数据
  myChart.setOption(
    {
      // 视觉映射组件，将数据映射到视觉元素(左下方数据组件)
      visualMap: {
        type: "continuous",
        show: false,
        min: minValue, //最小值
        max: maxValue, //最大值
        right: "3%", //组件离容器左侧的距离
        bottom: "5%", // 组件离容器下侧的距离
        calculable: true, //是否显示拖拽用的手柄（手柄能拖拽调整选中范围）
        seriesIndex: [0], //指定取哪个系列的数据，即哪个系列的 series.data
        inRange: {
          //定义 在选中范围中 的视觉元素
          color: ["#5cacd0", "#4aacfc", "#0c66c5", "#1230b9"],
        },
        textStyle: {
          //文字的颜色
          color: "#fff",
        },
      },
      geo3D: {
        show: false, //是否显示地理坐标系组件
        map: "china", //使用 registerMap 注册的地图名称 ,必须和 registerMap 注册的一致
        viewControl: {
          distance: 70,
          alpha: 60,
          center: "center", // 视角中心
          animation: true,
        },
      },
      series,
    },
    true
  );
  myChart.off("click");
  // // 绑定鼠标点击事件处理函数
  myChart.on("click", async (params) => {
    // 检查当前地图层级，如果已经达到第二层或更深，则不再响应点击
    if (parentInfo.value.length >= 2) {
      console.log("已达到第二层，无法继续下钻。");
      return;
    }

    let activeData = DATA.find((item) => item.name === params.name);
    if (activeData) {
      items.value = activeData.items;
      getGeoJson(params.data.cityCode, params.data);
    }
  });
}
const initEmyCharts = () => {
  if (myChart) myChart.dispose();
  // myChart = echarts.init(Left_C.value, "gl");
  myChart = echarts.init(Left_C.value);
  echarts.registerMap("china", geoJson); //注册
  seriesFn();
};
</script>

<style lang="scss" scoped>
.MapModule {
  .mapChoose {
    position: absolute;
    right: 0px;
    top: 0px;
    color: #eee;
    .button {
      position: relative;
      z-index: 88;
      padding: 1vmin 0.4vmin 1vmin 1.8vmin;
      border: none;
      background-color: transparent;
      cursor: pointer;
      outline: none;
      font-size: 14px;
    }

    .button.type1 {
      color: rgba(147, 235, 248, 0.8);
    }
  }
  .New_MapCard {
    position: absolute;
    left: 0;
    top: 0;
    height: auto;
    min-height: 60%;

    .card_box {
      height: 6vmin;
      // min-height: 66px;
    }
    .MapCard_item {
      position: relative;
      span {
        font-size: 1.3vmin;
      }
      .cara_span {
        padding: 0 2vmin 0 10px;
      }
      .item_text {
        position: absolute;
        padding: 0 10px 0 0;
        left: 0;
        top: 0;
        .cara_logo {
          width: 27%;
          min-width: 27%;
          img {
            height: 38%;
          }
        }
      }
    }
  }
}
</style>
