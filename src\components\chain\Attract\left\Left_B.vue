<!-- 左二 -->
<template>
  <div class="flex flex_col flex_J_SB por list_item h32">
    <p class="NB_foot_4 item_title">产业高地</p>
    <div style="width: 100%; height: 85%" ref="Left_B"></div>
  </div>
</template>

<script setup name="Left_B">
import * as echarts from "echarts";
import { ref, onMounted } from "vue";
let Left_B = ref(null);
var myChart_A;
onMounted(() => {
  getData();
});
function resizeHandler_A() {
  myChart_A.resize();
}
async function getData() {
  var currentIndex = 0;
  window.removeEventListener("resize", resizeHandler_A);
  window.addEventListener("resize", resizeHandler_A);
  let intervalId;
  clearInterval(intervalId);
  if (myChart_A != null && myChart_A != "" && myChart_A != undefined)
    myChart_A.dispose();
  myChart_A = echarts.init(Left_B.value);

  var option = {
    grid: {
      left: "5%", // 调整左边距以容纳左侧Y轴名称
      top: "20%",
      bottom: "14%",
      right: "5%", // 调整右边距以容纳右侧Y轴名称
      containLabel: true, // 确保标签包含在grid内
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        textStyle: {
          color: "#fff",
        },
      },
      formatter: function (params) {
        let str = params[0].axisValue + "<br/>";
        params.forEach((item) => {
          let unit = "";
          if (item.seriesName === "企业数量") {
            unit = "家";
          } else if (item.seriesName === "工业总产值") {
            unit = "亿";
          }
          str += `${item.marker} ${item.seriesName}: ${item.value} ${unit}<br/>`;
        });
        return str;
      },
    },
    legend: {
      data: ["企业数量", "工业总产值"],
      orient: "horizontal",
      top: "top",
      left: "center",
      itemHeight: 14,
      itemWidth: 18,
      itemGap: 20,
      textStyle: {
        color: "white",
      },
    },
    xAxis: {
      type: "category",
      data: ["专精特新", "规上企业", "国企央企"], // 更新X轴数据
      axisLine: {
        show: true,
        lineStyle: {
          type: "dashed",
          color: "rgba(244, 244, 244,0.1)",
        },
      },
      axisTick: {
        show: false,
      },
      offset: 4,
      axisLabel: {
        interval: 0,
        textStyle: {
          color: "white",
        },
        align: "center",
      },
    },
    yAxis: [
      {
        // 左侧Y轴 - 企业数量
        type: "value",
        name: "(家)",
        nameTextStyle: {
          color: "white",
          align: "right", // 将名称文本左对齐，使其更靠近左侧
        },
        min: 0,
        max: 50,
        interval: 10,
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          fontSize: 14,
          textStyle: {
            color: "white",
          },
        },
        splitLine: {
          show: true,
          lineStyle: { type: "dashed", color: "rgba(77, 128, 254, 0.2)" },
        },
      },
      {
        // 右侧Y轴 - 工业总产值
        type: "value",
        name: "(亿)",
        nameTextStyle: {
          // 为右侧Y轴名称添加样式
          color: "white",
          align: "left", // 将名称文本右对齐
        },
        // nameLocation: 'end', // 默认就是 'end'，可以不写
        nameGap: 20, // 调整名称与轴线的距离
        min: 0,
        max: 500,
        interval: 100,
        position: "right",
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          fontSize: 14,
          textStyle: {
            color: "white",
          },
        },
        splitLine: {
          show: false, // 右侧Y轴不显示分割线
        },
      },
    ],
    series: [
      {
        name: "企业数量",
        type: "bar",
        yAxisIndex: 0, // 对应左侧Y轴
        data: [32, 42, 12], // 估算数据
        barWidth: "25%",
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "rgb(4, 27, 68)", // 柱状图渐变色
              },
              {
                offset: 1,
                color: "#0f82f8",
              },
            ]),
            borderColor: "#0f82f8", // 添加浅蓝色边框
            borderWidth: 1, // 边框宽度
          },
        },
        label: {
          show: false, // 图片中柱状图没有显示数值
        },
      },
      {
        name: "工业总产值",
        type: "line",
        yAxisIndex: 1, // 对应右侧Y轴
        data: [380, 480, 120], // 估算数据
        smooth: true, // 平滑曲线
        symbol: "circle", // 节点为圆点
        symbolSize: 8,
        lineStyle: {
          color: "#00CED1", // 青色线条
          width: 2,
        },
        itemStyle: {
          color: "#00CED1", // 节点颜色
        },
        areaStyle: {
          // 区域填充
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: "rgba(0, 206, 209, 0.4)", // 区域渐变起始色 (带透明度)
            },
            {
              offset: 1,
              color: "rgba(0, 206, 209, 0)", // 区域渐变结束色 (全透明)
            },
          ]),
        },
        label: {
          show: false, // 图片中折线图没有显示数值
        },
      },
    ],
  };
  myChart_A.setOption(option);

  // 调整自动高亮逻辑以适配新的系列
  intervalId = setInterval(() => {
    var dataLen = option.series[0].data.length; // 使用第一个系列的数据长度
    currentIndex = (currentIndex + 1) % dataLen;

    // 遍历所有系列进行高亮和显示tooltip
    option.series.forEach((series, index) => {
      myChart_A.dispatchAction({
        type: "highlight",
        seriesIndex: index,
        dataIndex: currentIndex,
      });

      myChart_A.dispatchAction({
        type: "showTip",
        seriesIndex: index,
        dataIndex: currentIndex,
      });
    });
  }, 4000);
}
</script>
