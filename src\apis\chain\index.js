// 链万企接口
import request from "@/http/chain/request";
const API = {
  Login_Port: (params) =>
    request({
      url: "/auth/loginBySmsCode",
      method: "POST",
      data: params,
      headers: {
        screenCode: "289aa5c3c61546928b63eab0302dc248",
      },
    }),
  //  获取资源
  GET_LeftB_Port: (params) =>
    request({
      url: "system/supply/getTotalNum",
      method: "GET",
      params,
    }),
  //  获取需求
  GET_LeftA_Port: (params) =>
    request({
      url: "system/demand/getTotalNum",
      method: "GET",
      params,
    }),
  GET_NavC_Port: (params) =>
    request({
      url: "system/company/mag/secret/getCompanylist_cxhl_list",
      method: "GET",
      params,
    }),
  GET_NavD_Port: (params) =>
    request({
      url: "system/information/listByText",
      method: "GET",
      params,
    }),
  GET_NavE_Port: (params) =>
    request({
      url: "system/activity-portal/list",
      method: "GET",
      params,
    }),
  GET_Enterprise_Port: (params) =>
    request({
      url: "system/company/mag/secret/getCompanylist_cxhl_list",
      method: "GET",
      params,
    }),
  POST_PolicyList_Port: (params) =>
    request({
      url: "system/policy/listByText?pageNum=1&pageSize=1",
      method: "POST",
      data: params,
    }),
  GET_Platform_Port: (params) =>
    request({
      url: "system/web/wq_plat/list",
      method: "GET",
      params,
    }),
};
export default API;
