<!--  -->
<template>
  <div class="flex flex_col flex_j_SB over list_item h32">
    <p class="NB_foot_4 item_title">各县企业工业增加值增速</p>
    <div class="table_head flex flex_J_SB">
      <div
        class="flex flex_J_C flex_A_C table_headA fs14- font_noWarp text_noWrapA"
      >
        排名
      </div>
      <div class="flex flex_J_C flex_A_C fs14 table_headB tc por head2">
        区域
      </div>
      <div class="flex flex_J_C flex_A_C fs14 table_headC tc text_l">
        产业数字化发展水平指数
      </div>
    </div>
    <div class="table_body flex1 over">
      <vue3-seamless-scroll
        direction="up"
        :hover="true"
        :isWatch="true"
        :limitScrollNum="right_one_list.length"
        :singleLine="true"
        :list="right_one_list"
        :step="0.4"
        class="DongTable flex flex_col"
      >
        <ul class="flex flex_col flex_J_SB">
          <li
            v-for="(item, i) in right_one_list"
            :key="i"
            class="flex flex_J_SB item_row tr"
            :class="[i % 2 != 1 ? 'row_j' : 'row_t']"
          >
            <div class="flex flex_J_C flex_A_C table_headA">
              <span class="w100 tc fs14- text_noWrapA">
                <!-- {{ item.name || "暂未填写" }} -->
                {{ i + 1 }}
              </span>
            </div>
            <div class="flex flex_A_C fs14- table_headB body_b">
              <span class="w100 tc text_noWrapA">
                {{ item.location || "暂未填写" }}
              </span>
            </div>
            <div class="flex flex_J_C flex_A_C fs14 table_headC body_b">
              {{ item.tag || "暂未填写" }}
            </div>
          </li>
        </ul>
      </vue3-seamless-scroll>
    </div>
  </div>
</template>

<script setup name="Right_C">
import { ref, nextTick, onMounted, onBeforeUnmount } from "vue";
let right_one_list = ref([
  {
    location: "吉安县",
    tag: "15.6%",
  },
  {
    location: "吉州区",
    tag: "14.9%",
  },
  {
    location: "万安县",
    tag: "14.1%",
  },
  {
    location: "遂川县",
    tag: "13.2%",
  },
  {
    location: "井冈山市",
    tag: "12.7%",
  },
  {
    location: "吉安市",
    tag: "12.5%",
  },
  {
    location: "峡江县",
    tag: "11%",
  },
  {
    location: "安福县",
    tag: "10.8% ",
  },
  {
    location: "泰和县",
    tag: "10%",
  },
  {
    location: "永新县",
    tag: "9.5%",
  },
  {
    location: "吉水县",
    tag: "8.7%",
  },
  {
    location: "青原区",
    tag: "7%",
  },
  {
    location: "永丰县",
    tag: "6%",
  },
  {
    location: "新干县",
    tag: "6%",
  },
]);
</script>

<style scoped lang="scss">
.table_head {
  background-color: #015bff;
  height: 34px;
  min-height: 34px;
  margin-top: 0.8vmin;
}
.head1 {
  padding-left: 10%;
  &::before {
    content: "";
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1.2px;
    height: 60%;
    background-color: #fff;
  }
}

.table_headA {
  width: 15%;
  text-align: left;
}
.table_headC {
  width: 44%;
}
.table_headB {
  width: 20%;
}
.table_body {
  .item_row {
    height: 34px;
    &:hover {
      background-color: rgba(135, 206, 235, 0.2);
    }
  }
  ::v-deep .DongTable {
    & > div {
      display: flex;
      flex-direction: column;
      will-change: transform;
    }
  }
  .row_t {
    color: #718bbd;
  }
  .row_j {
    background: rgba(1, 91, 255, 0.2);
  }
}
</style>
