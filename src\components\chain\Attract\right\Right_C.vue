<!--  -->
<template>
  <div class="flex flex_col flex_j_SB over list_item h32 AttractTable">
    <p class="NB_foot_4 item_title">风险分析</p>
    <div class="table_head flex flex_J_SB">
      <div class="flex flex_J_C flex_A_C fs14 table_headB tc por head2">
        企业名称
      </div>
      <div class="flex flex_J_C flex_A_C fs14 table_headC tc text_l">
        风险类型
      </div>
    </div>
    <div class="table_body flex1 over">
      <vue3-seamless-scroll
        direction="up"
        :hover="true"
        :isWatch="true"
        :limitScrollNum="right_one_list.length"
        :singleLine="true"
        :list="right_one_list"
        :step="0.4"
        class="DongTable flex flex_col"
      >
        <ul class="flex flex_col flex_J_SB">
          <li
            v-for="(item, i) in right_one_list"
            :key="i"
            class="flex flex_J_SB item_row tr"
            :class="[i % 2 != 1 ? 'row_j' : 'row_t']"
          >
            <div class="flex flex_A_C fs14- table_headB body_b">
              <span class="w100 tc text_noWrapA">
                {{ item.location || "暂未填写" }}
              </span>
            </div>
            <div class="flex flex_J_C flex_A_C fs14 table_headC body_b">
              {{ item.tag || "暂未填写" }}
            </div>
          </li>
        </ul>
      </vue3-seamless-scroll>
    </div>
  </div>
</template>

<script setup name="Right_C">
import { ref, nextTick, onMounted, onBeforeUnmount } from "vue";
let right_one_list = ref([
  {
    location: "贞丰县恒山建材有限公司",
    tag: "失信人",
  },
  {
    location: "贵州同威生物科技有限公司",
    tag: "其他风险-522超标",
  },
  {
    location: "贵州科测信息技术有限责任公司",
    tag: "经营异常",
  },
  {
    location: "贵州艾力康中草药开发有限公司",
    tag: "失信人",
  },
  {
    location: "道真仡佬族苗族自治县万福农业.",
    tag: "经营异常",
  },
]);
</script>

<style scoped lang="scss">
.AttractTable {
  .table_head {
    background-color: #015bff;
    height: 34px;
    min-height: 34px;
    margin-top: 0.8vmin;
  }
  .head1 {
    padding-left: 10%;
    &::before {
      content: "";
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 1.2px;
      height: 60%;
      background-color: #fff;
    }
  }
  .table_headC {
    width: 44%;
  }
  .table_headB {
    width: 44%;
  }
  .table_body {
    .item_row {
      height: 34px;
      &:hover {
        background-color: rgba(135, 206, 235, 0.2);
      }
    }
    ::v-deep .DongTable {
      & > div {
        display: flex;
        flex-direction: column;
        will-change: transform;
      }
    }
    .row_t {
      color: #718bbd;
    }
    .row_j {
      background: rgba(1, 91, 255, 0.2);
    }
  }
}
</style>
