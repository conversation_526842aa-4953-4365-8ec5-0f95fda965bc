<script setup>
import { nextTick, ref, onMounted } from "vue";
import { Panel, VueFlow, useVueFlow } from "@vue-flow/core";
import { Background } from "@vue-flow/background";
import { initialEdges, initialNodes } from "../VueFlow/initial-elements.js";
import { useLayout } from "../VueFlow/useLayout";
const nodes = ref(initialNodes);
const edges = ref(initialEdges);
const { layout } = useLayout();
const { fitView, setInteractive } = useVueFlow();
const showTooltip = ref(false);
const tooltipContent = ref("");
const tooltip = ref(null); // Tooltip element

async function layoutGraph(direction) {
  // 假设 layout() 函数是异步的，需要使用 await 等待其结果
  const newNodes = await layout(nodes.value, edges.value, direction);
  nodes.value = newNodes; // 将布局后的节点赋值给响应式引用

  nextTick(() => {
    fitView({
      minZoom: 0.7,
    });
  });
}

onMounted(() => {
  setInteractive(false); // 禁用所有交互
});
let x;
let y;
function onNodeMouseEnter(node) {
  x = node.event.layerX;
  y = node.event.layerY;
  tooltipContent.value = node.label; // 获取节点标签，确保显示正确内容
  showTooltip.value = true; // 显示tooltip
}

function onNodeMouseLeave() {
  showTooltip.value = false; // 隐藏tooltip
}
</script>

<template>
  <div class="layout-flow por">
    <VueFlow
      :nodes="nodes"
      :edges="edges"
      @nodes-initialized="layoutGraph('LR')"
      @nodeMouseEnter="onNodeMouseEnter"
      @nodeMouseLeave="onNodeMouseLeave"
    >
      <Background />
      <div
        class="tooltip"
        ref="tooltip"
        :style="{
          opacity: showTooltip ? 1 : 0,
          top: y + 'px',
          left: x + 'px',
        }"
      >
        <div class="tooltip-content">{{ tooltipContent }}</div>
      </div>
    </VueFlow>
  </div>
</template>

<style lang="scss">
.layout-flow {
  height: 100%;
  width: 100%;
  /* 确保没有 margin-left，以符合图片布局 */
  .node_0 {
    color: #fff;
    background-color: #2bd4ab;
  }
  .node_1 {
    color: #fff;
    background-color: #36b4ec;
  }
  .tooltip {
    position: absolute;
    will-change: left, top, opacity;
    transition: top 0.3s ease, left 0.3s ease, opacity 0.2s ease-in;
    background-color: #f4f7fe;
    padding: 8px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 10;
    pointer-events: none;
    .tooltip-content {
      text-align: center;
      color: #000;
    }
  }
}
</style>
