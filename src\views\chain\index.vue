<!-- 主要页面 -->
<template>
  <div class="Home">
    <section class="flex flex_J_C flex_A_C h100" v-if="upload">
      <div class="loading">
        <div v-for="i in 10" :key="i"></div>
      </div>
      <h5 class="NB_foot_6 fs20" ref="logingDom"></h5>
    </section>
    <div class="Homebox" v-else>
      <nav class="animate__animated animate__fadeInDown">
        <img src="@/assets/image/chain/nav.png" alt="" />
        <h1 class="NB_foot_3" ref="myDom" id="myp"></h1>
        <div class="btnBtn">
          <div
            @click="onClickFn('/list', '产业全景')"
            :class="[route.meta.user === '产业全景' ? 'activeBtn' : '']"
            class="btn btn-right position1"
          >
            产业全景
          </div>
          <div
            @click="onClickFn('/watch', '产业监测')"
            :class="[route.meta.user === '产业监测' ? 'activeBtn' : '']"
            class="btn btn-right position2"
          >
            产业监测
          </div>
          <div
            @click="onClickFn('/attract', '产业招商')"
            :class="[route.meta.user === '产业招商' ? 'activeBtn' : '']"
            class="btn btn-left position3"
          >
            产业招商
          </div>
        </div>
      </nav>
      <main class="flex flex_col animate__animated animate__fadeInDown">
        <ol v-if="route.query.name" class="headerBox flex flex_J_SB">
          <li class="flex flex_J_C" v-for="(item, i) in headerList" :key="i">
            <div class="header_l">
              <img src="@/assets/image/chain/Tag.png" class="w100" alt="" />
              <span
                class="iconfont header_span"
                :class="item.class"
                style="color: #fff"
              ></span>
            </div>
            <div class="header_r flex flex_col flex_J_SE h100">
              <span class="fs20 NB_foot_3">{{ item.user }}</span>
              <p class="fc_9 flex flex_A_C NB_foot_9">
                <count-up
                  class="fs18"
                  :startVal="0"
                  :endVal="item.num"
                  :duration="3"
                  :loop="true"
                  :delay="12"
                />
                <span class="fs16"> 个</span>
              </p>
            </div>
          </li>
        </ol>
        <router-view></router-view>
      </main>
    </div>
  </div>
</template>

<script setup name="HomeView">
import { useRouter, useRoute } from "vue-router";
import CountUp from "vue-countup-v3";
import { ref, nextTick, onMounted } from "vue";
import useCounter from "@/stores/chain/chain.js";
const route = useRoute();
const router = useRouter();
const stores = useCounter();
let upload = ref(true);
let myDom = ref(null);
let logingDom = ref(null);
let headerList = ref([]);
var LongTime;
var login_time;
var login_text_time;
var timerId;
function onClickFn(item, text) {
  console.log(item);
  router.push(item);
}
onMounted(() => {
  console.log("🚀 ~ route:", route);

  Login_Typing_Style_Effect("数据加载中....");
  getData();
});

async function getData() {
  // if (route.query.name) {
  //   await stores.GetUserNameData(route.query.name);
  // } else {
  //   await stores.GetAllData();
  // }
  executeFn();
  headerList.value = stores.nav_data;
  clearInterval(login_time);
  clearInterval(login_text_time);
}

function Login_Typing_Style_Effect(text) {
  let time = 180;
  clearInterval(login_time);
  login_time = setInterval(() => dong(), time * text.length + 1);
  dong();
  function dong() {
    if (login_text_time) clearInterval(login_text_time);
    nextTick(() => {
      if (!logingDom.value) return;
      const contentArr = text.split("");
      let content = "";
      let index = 0;
      login_text_time = setInterval(() => {
        content += contentArr[index];
        logingDom.value.innerHTML = content + "_";
        index++;
        if (index === contentArr.length) {
          logingDom.value.innerHTML = content;
          clearInterval(login_text_time);
          login_text_time = null;
        }
      }, time);
    });
  }
}
function executeFn() {
  let text =
    // route.query.name
    // ? `${route.query.name}区链万企公共服务平台` :
    "吉安数字视听产业大脑可视化大屏";
  upload.value = false;
  Typing_Style_Effect(text);
  clearInterval(LongTime);

  LongTime = setInterval(() => {
    Typing_Style_Effect(text);
  }, 10000);
}
function Typing_Style_Effect(text) {
  if (timerId) clearInterval(timerId);
  nextTick(() => {
    const myp = myDom.value;
    if (!myp) return;
    const contentArr = text.split("");
    let content = "";
    let index = 0;
    timerId = setInterval(() => {
      content += contentArr[index];
      myp.innerHTML = content + "_";
      index++;
      if (index === contentArr.length) {
        myp.innerHTML = content;
        clearInterval(timerId);
        timerId = null;
      }
    }, 250);
  });
}
</script>

<style lang="scss">
@import "./Home.scss";
</style>
