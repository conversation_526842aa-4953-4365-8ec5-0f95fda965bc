// 公用方法函数
/* 
  import METHOD from "@/utils/method/index.js";
*/
const METHOD = {
  // 获取近七天日期 用_拼接数组['10_1']
  getRecentSevenDays(_) {
    let dateArray = [];
    for (let i = 6; i >= 0; i--) {
      let date = new Date();
      date.setDate(date.getDate() - i);
      let month = (date.getMonth() + 1).toString().padStart(2, "0");
      let day = date.getDate().toString().padStart(2, "0");
      dateArray.push(`${month}${_}${day}`);
    }
    return dateArray;
  },
  // 随机获取青岛市坐标
  getRandomQingdaoCoordinates() {
    // 青岛市陆地边界多边形（简化坐标范围）
    const qingdaoArea = [
      [120.25, 36.0], // 黄岛西部边界
      [120.4, 36.0], // 胶州湾入口
      [120.5, 36.15], // 崂山区东部
      [120.55, 36.3], // 即墨交界
      [120.3, 36.35], // 城阳北部
      [120.15, 36.2], // 胶州湾西北部
      [120.25, 36.0], // 闭合多边形
    ];

    // 定义多边形经纬度极限范围（加速随机点生成）
    const minLongitude = Math.min(...qingdaoArea.map((p) => p[0]));
    const maxLongitude = Math.max(...qingdaoArea.map((p) => p[0]));
    const minLatitude = Math.min(...qingdaoArea.map((p) => p[1]));
    const maxLatitude = Math.max(...qingdaoArea.map((p) => p[1]));

    // 射线法判断点是否在多边形内
    function isInPolygon(point, polygon) {
      const [x, y] = point;
      let inside = false;
      for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
        const [xi, yi] = polygon[i];
        const [xj, yj] = polygon[j];
        const intersect =
          yi > y !== yj > y && x < ((xj - xi) * (y - yi)) / (yj - yi) + xi;
        if (intersect) inside = !inside;
      }
      return inside;
    }

    // 生成有效坐标
    while (true) {
      const lng = Math.random() * (maxLongitude - minLongitude) + minLongitude;
      const lat = Math.random() * (maxLatitude - minLatitude) + minLatitude;
      if (isInPolygon([lng, lat], qingdaoArea)) {
        return [lng.toFixed(6), lat.toFixed(6)]; // 保留6位小数精度
      }
    }
  },
  // 随机颜色 16进制
  getRandomHexColor() {
    const color = (((1 << 24) * Math.random()) | 0)
      .toString(16)
      .padStart(6, "0");
    const alpha = Math.round(Math.random() * 255)
      .toString(16)
      .padStart(2, "0");
    return `#${color}${alpha}`.toUpperCase();
  },
  // 16进制随机颜色
  getRandomHexColor_16() {
    const letters = "0123456789ABCDEF";
    let color = "#";
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
  },
  // 随机数字
  suijiNumFn(max, min) {
    return Math.floor(Math.random() * (max - min + 1) + min);
  },
  // 随机三原色 rgba
  getRandomRgbaRGBA(T) {
    const r = Math.floor(Math.random() * 256);
    const g = Math.floor(Math.random() * 256);
    const b = Math.floor(Math.random() * 256);
    const a = (Math.random() * (1 - 0.1) + 0.1).toFixed(1);
    return `rgba(${r}, ${g}, ${b}, ${T})`;
  },
  magicMouse(e) {
    const body = document.querySelector("body");
    const element = document.getElementById("g-pointer-1");
    const element2 = document.getElementById("g-pointer-2");
    const halfAlementWidth = element.offsetWidth / 2;
    const halfAlementWidth2 = element2.offsetWidth / 2;

    let isHovering = false;

    // 判断元素是否悬停在具有类名为 .g-animation 的元素上
    window.addEventListener("mouseover", (event) => {
      const target = event.target;

      // 只在鼠标进入 g-animation 元素时生效
      if (target.closest(".g-animation") && !isHovering) {
        isHovering = true;
        const rect = target.getBoundingClientRect();
        const style = window.getComputedStyle(target);

        element2.style.width = `${rect.width + 4}px`;
        element2.style.height = `${rect.height + 4}px`;
        element2.style.borderRadius = `${style.borderRadius}`;
        element2.style.transform = `translate(${rect.left - 2}px, ${
          rect.top - 2
        }px)`;
      }
    });

    // 判断鼠标是否离开整个 g-animation 元素时复原
    window.addEventListener("mouseout", (event) => {
      const target = event.target;

      // 只有当鼠标离开整个 g-animation 元素时才复原
      if (!target.closest(".g-animation") && isHovering) {
        isHovering = false;

        // 样式复原
        element2.style.width = `42px`;
        element2.style.height = `42px`;
        element2.style.borderRadius = `50%`;
      }
    });

    // 用于控制两个鼠标指针元素
    body.addEventListener("mousemove", (e) => {
      window.requestAnimationFrame(function () {
        setPosition(e.clientX, e.clientY);
      });
    });

    function setPosition(x, y) {
      window.requestAnimationFrame(function () {
        element.style.transform = `translate(${x - halfAlementWidth}px, ${
          y - halfAlementWidth
        }px)`;

        if (!isHovering) {
          element2.style.transform = `translate(${x - halfAlementWidth2}px, ${
            y - halfAlementWidth2
          }px)`;
        }
      });
    }
  },
};

export default METHOD;
