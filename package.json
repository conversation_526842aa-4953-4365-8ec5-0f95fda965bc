{"name": "PlatformEcologicalPowerOperationCenter", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"ant-design-vue": "^4.2.6", "@dagrejs/dagre": "^1.1.4", "@vue-flow/background": "^1.3.2", "@vue-flow/core": "^1.42.4", "@vue-flow/node-toolbar": "^1.1.1", "axios": "^1.7.2", "crypto-js": "^4.2.0", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.1.0", "pinia": "^2.1.7", "video.js": "^8.19.1", "videojs-contrib-hls": "^5.15.0", "vue": "^3.2.13", "vue-countup-v3": "^1.4.1", "vue-router": "^4.0.3", "vue3-seamless-scroll": "^2.0.1"}, "devDependencies": {"@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "compression-webpack-plugin": "^11.1.0", "sass": "^1.32.7", "sass-loader": "^12.0.0"}}