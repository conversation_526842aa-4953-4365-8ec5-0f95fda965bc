<!-- 左二 -->
<template>
  <div class="flex flex_col flex_J_SB por list_item h32">
    <p class="NB_foot_4 item_title">企业结构</p>
    <div style="width: 100%; height: 85%" ref="Left_B"></div>
  </div>
</template>

<script setup name="Left_B">
import * as echarts from "echarts";
import { ref, onMounted } from "vue";
import useCounter from "@/stores/chain/chain.js";
let Left_B = ref(null);
var myChart;
onMounted(() => {
  getData();
  // list.value = stores.need_data;
});
function resizeHandler() {
  myChart.resize();
}
async function getData() {
  window.removeEventListener("resize", resizeHandler);
  window.addEventListener("resize", resizeHandler);
  myChart = echarts.init(Left_B.value);
  const option = {
    /* 
      换算比例 = 100 / 0.4 = 250
      value_A = 0.3 * 250 = 75
      value_C = 0.2 * 250 = 50
      ..
     */
    color: ["#029BDA", "#3B478F", "#8593D0", "#24C39D"],
    // tooltip: {
    //   trigger: "axis",
    //   axisPointer: {
    //     type: "line",
    //   },
    // },
    radar: {
      name: {
        show: false,
      },
      indicator: [
        {
          name: "A",
          max: 100,
        },
        {
          name: "B",
          max: 100,
        },
        {
          name: "C",
          max: 100,
        },
        {
          name: "D",
          max: 100,
        },
        {
          name: "E",
          max: 100,
        },
        {
          name: "F",
          max: 100,
        },
        {
          name: "G",
          max: 100,
        },
        {
          name: "H",
          max: 100,
        },
      ],
      center: ["52%", "11%"],
      radius: "150%",
      axisLine: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      splitArea: {
        show: false,
      },
    },
    // legend: {
    //   left: "0%",
    //   top: "0%",
    //   // textStyle: {
    //   //   color: "#fff",
    //   //   fontSize: 13,
    //   // },
    // },
    series: [
      {
        type: "radar",
        areaStyle: {
          opacity: 1,
          shadowBlur: 1,
          shadowColor: "rgba(0,0,0,.5)",
        },
        emphasis: {
          areaStyle: {
            opacity: 0,
          },
        },
        silent: true,
        data: [
          {
            value: [0, 0, 0, 100, 100, 99, 0, 0],
            name: "全量企业",
            symbol: "circle",
            symbolSize: 1,
            label: {
              textStyle: {
                color: "#fff",
                fontWeight: "bold",
              },
              show: true,
              position: [-316, -10],
              formatter: function (point) {
                if (point.value == 99)
                  return point.name + "81家，占比100%" + "——";
                else return "";
              },
            },
          },
          {
            value: [0, 0, 0, 75, 75, 74, 0, 0],
            name: "国家高新技术企业",
            symbol: "circle",
            symbolSize: 1,
            label: {
              show: true,
              position: [-5, -10],
              textStyle: {
                color: "#fff",
                fontWeight: "bold",
              },
              formatter: function (point) {
                if (point.value == 74) return "——" + "15家，占比18.52%";
                else return "";
              },
            },
          },
          {
            value: [0, 0, 0, 50, 50, 49, 0, 0],
            name: "国家级专精特新",
            symbol: "circle",
            symbolSize: 1,
            label: {
              textStyle: {
                color: "#fff",
                fontWeight: "bold",
              },
              show: true,
              position: [-274, -10],
              formatter: function (point) {
                if (point.value == 49)
                  return point.name + "4家，占比4.94%" + "——";
                else return "";
              },
            },
          },
          {
            value: [0, 0, 0, 25, 25, 24, 0, 0],
            name: "上市企业",
            symbol: "circle",
            symbolSize: 1,
            label: {
              textStyle: {
                color: "#fff",
                fontWeight: "bold",
              },
              show: true,
              position: [-5, -10],
              formatter: function (point) {
                return point.value == 24
                  ? "——" + point.name + "3家，占比3.7%"
                  : "";
              },
            },
          },
        ],
      },
    ],
    itemStyle: {
      emphasis: {
        show: false,
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: "rgba(0, 0, 0, 0.5)",
      },
    },
  };
  myChart.setOption(option, true);
}
</script>
