const position = { x: 0, y: 0 };

export let initialNodes = [
  // 供应端
  {
    id: "supply",
    position,
    data: {
      label: "供应端",
    },
    class: "node_0",
  },
  {
    id: "led_materials",
    position,
    data: {
      label: "LED封装材料",
    },
    class: "node_1",
  },
  {
    id: "substrate",
    position,
    data: {
      label: "衬底基板",
    },
    class: "node_1",
  },
  {
    id: "led_chips",
    position,
    data: {
      label: "LED芯片",
    },
    class: "node_1",
  },
  {
    id: "led_driver",
    position,
    data: {
      label: "引线框架",
    },
    class: "node_1",
  },
  {
    id: "packaging",
    position,
    data: {
      label: "封装胶",
    },
    class: "node_1",
  },
  {
    id: "low_temp_materials",
    position,
    data: {
      label: "低温填充料",
    },
    class: "node_1",
  },
  {
    id: "led_package",
    position,
    data: {
      label: "LED封装",
    },
    class: "node_1",
  },
  {
    id: "sapphire_materials",
    position,
    data: {
      label: "蓝宝石材料",
    },
    class: "node_1",
  },
  {
    id: "sapphire_components",
    position,
    data: {
      label: "蓝宝石元件",
    },
    class: "node_1",
  },
  {
    id: "electronic_components",
    position,
    data: {
      label: "电子元器件",
    },
    class: "node_1",
  },
  {
    id: "electronic_thermal",
    position,
    data: {
      label: "电子散热器",
    },
    class: "node_1",
  },
  {
    id: "led_optical_materials",
    position,
    data: {
      label: "LED背光源及材料",
    },
    class: "node_1",
  },
  {
    id: "led_backlight",
    position,
    data: {
      label: "LED背光源",
    },
    class: "node_1",
  },
  {
    id: "ccfl_backlight",
    position,
    data: {
      label: "CCFL背光源",
    },
    class: "node_1",
  },
  {
    id: "mobile_backlight",
    position,
    data: {
      label: "手机背光源",
    },
    class: "node_1",
  },
  {
    id: "display_backlight",
    position,
    data: {
      label: "显示器背光源",
    },
    class: "node_1",
  },
  {
    id: "display_panel_equipment",
    position,
    data: {
      label: "显示面板设备",
    },
    class: "node_1",
  },
  {
    id: "single_board",
    position,
    data: {
      label: "单板玻璃",
    },
    class: "node_1",
  },
  {
    id: "cover_board",
    position,
    data: {
      label: "盖板玻璃",
    },
    class: "node_1",
  },
  {
    id: "current_board",
    position,
    data: {
      label: "导电玻璃",
    },
    class: "node_1",
  },
  {
    id: "protection_board",
    position,
    data: {
      label: "保护玻璃",
    },
    class: "node_1",
  },
  {
    id: "led_drive_power",
    position,
    data: {
      label: "LED驱动电源",
    },
    class: "node_1",
  },
  {
    id: "led_control_system",
    position,
    data: {
      label: "LED控制系统",
    },
    class: "node_1",
  },
  {
    id: "led_bracket",
    position,
    data: {
      label: "LED支架",
    },
    class: "node_1",
  },
  {
    id: "led_extension",
    position,
    data: {
      label: "LED延片",
    },
    class: "node_1",
  },
  {
    id: "external_extension",
    position,
    data: {
      label: "外延延片",
    },
    class: "node_1",
  },
  {
    id: "storage_extension",
    position,
    data: {
      label: "储存延片",
    },
    class: "node_1",
  },
  // 生产端
  {
    id: "production",
    position,
    data: {
      label: "生产端",
    },
    class: "node_0",
  },
  {
    id: "led_display",
    position,
    data: {
      label: "LED显示屏",
    },
    class: "node_1",
  },
  {
    id: "monochrome_led",
    position,
    data: {
      label: "单色LED显示屏",
    },
    class: "node_1",
  },
  {
    id: "led_text",
    position,
    data: {
      label: "LED图文屏",
    },
    class: "node_1",
  },
  {
    id: "led_video",
    position,
    data: {
      label: "LED视频屏",
    },
    class: "node_1",
  },
  {
    id: "led_indoor",
    position,
    data: {
      label: "LED室内屏",
    },
    class: "node_1",
  },
  {
    id: "led_lighting_tools",
    position,
    data: {
      label: "LED照明灯具",
    },
    class: "node_1",
  },
  {
    id: "led_landscape_light",
    position,
    data: {
      label: "LED景观灯",
    },
    class: "node_1",
  },
  {
    id: "led_bulb",
    position,
    data: {
      label: "LED球泡灯",
    },
    class: "node_1",
  },
  {
    id: "led_strip_light",
    position,
    data: {
      label: "LED灯条",
    },
    class: "node_1",
  },
  {
    id: "led_high_power",
    position,
    data: {
      label: "LED大功率灯",
    },
    class: "node_1",
  },
  {
    id: "automotive_led",
    position,
    data: {
      label: "汽车灯具配件",
    },
    class: "node_1",
  },
  {
    id: "headlight",
    position,
    data: {
      label: "前照灯",
    },
    class: "node_1",
  },
  {
    id: "work_light",
    position,
    data: {
      label: "车工作灯",
    },
    class: "node_1",
  },
  {
    id: "brake_light",
    position,
    data: {
      label: "车尾灯",
    },
    class: "node_1",
  },
  {
    id: "indicator_light",
    position,
    data: {
      label: "示廓灯",
    },
    class: "node_1",
  },
  // 应用端
  {
    id: "application",
    position,
    data: {
      label: "应用端",
    },
    class: "node_0",
  },
  {
    id: "display_application_system",
    position,
    data: {
      label: "显示屏应用系统",
    },
    class: "node_1",
  },
  {
    id: "aviation_display_system",
    position,
    data: {
      label: "航空显示系统",
    },
    class: "node_1",
  },
  {
    id: "display_usage_system",
    position,
    data: {
      label: "显示屏用接系统",
    },
    class: "node_1",
  },
  {
    id: "industrial_display_equipment",
    position,
    data: {
      label: "工控显示设备",
    },
    class: "node_1",
  },
  {
    id: "optical_standard_display_system",
    position,
    data: {
      label: "光电标准显示系统",
    },
    class: "node_1",
  },
  {
    id: "lighting_engineering",
    position,
    data: {
      label: "照明工程",
    },
    class: "node_1",
  },
  {
    id: "urban_lighting_engineering",
    position,
    data: {
      label: "城市照明工程",
    },
    class: "node_1",
  },
  {
    id: "stage_lighting_engineering",
    position,
    data: {
      label: "舞台灯光工程",
    },
    class: "node_1",
  },
  {
    id: "stage_lighting",
    position,
    data: {
      label: "舞台灯具",
    },
    class: "node_1",
  },
  {
    id: "video_display_service",
    position,
    data: {
      label: "影视显示服务",
    },
    class: "node_1",
  },
  {
    id: "storage_equipment_service",
    position,
    data: {
      label: "仓储设备及服务",
    },
    class: "node_1",
  },
  {
    id: "special_vehicles",
    position,
    data: {
      label: "专用车辆",
    },
    class: "node_1",
  },
  {
    id: "travel_vehicle",
    position,
    data: {
      label: "旅行车",
    },
    class: "node_1",
  },
  {
    id: "suv_vehicle",
    position,
    data: {
      label: "越野车用车",
    },
    class: "node_1",
  },
  {
    id: "super_suv_vehicle",
    position,
    data: {
      label: "超级越野车",
    },
    class: "node_1",
  },
  {
    id: "special_purpose_vehicle",
    position,
    data: {
      label: "专用商用车",
    },
    class: "node_1",
  },
];

export const initialEdges = [
  // 第一层到第二层的连接
  { id: "e1-2", source: "digital_audiovisual", target: "components" },
  { id: "e1-3", source: "digital_audiovisual", target: "manufacturing" },
  { id: "e1-4", source: "digital_audiovisual", target: "terminal_services" },

  // 第二层（零部件）到第三层的连接
  { id: "e2-5", source: "components", target: "electronic_components" },
  { id: "e2-6", source: "components", target: "functional_components" },
  { id: "e2-7", source: "components", target: "modules" },

  // 第二层（整机制造）到第三层的连接
  { id: "e3-8", source: "manufacturing", target: "consumer_terminals" },
  { id: "e3-9", source: "manufacturing", target: "non_consumer_terminals" },
  { id: "e3-10", source: "manufacturing", target: "other_terminal_manufacturing" },

  // 第二层（智能终端相关服务）到第三层的连接
  { id: "e4-11", source: "terminal_services", target: "software_development" },
  { id: "e4-12", source: "terminal_services", target: "telecom_services" },
  { id: "e4-13", source: "terminal_services", target: "product_development_services" },
  { id: "e4-14", source: "terminal_services", target: "testing_certification" },
  { id: "e4-15", source: "terminal_services", target: "other_terminal_services" },
  { id: "e4-16", source: "terminal_services", target: "customer_management" },

  // 第三层（电子元器件）到第四层的连接
  { id: "e5-17", source: "electronic_components", target: "soc" },
  { id: "e5-18", source: "electronic_components", target: "cpu" },
  { id: "e5-19", source: "electronic_components", target: "gpu" },
  { id: "e5-20", source: "electronic_components", target: "baseband_chip" },
  { id: "e5-21", source: "electronic_components", target: "microprocessor" },
  { id: "e5-22", source: "electronic_components", target: "touch_control_chip" },
  { id: "e5-23", source: "electronic_components", target: "display_driver_chip" },
  { id: "e5-24", source: "electronic_components", target: "audio_codec" },
  { id: "e5-25", source: "electronic_components", target: "navigation_chip" },
  { id: "e5-26", source: "electronic_components", target: "power_management_chip" },
  { id: "e5-27", source: "electronic_components", target: "memory" },
  { id: "e5-28", source: "electronic_components", target: "sensor" },
  { id: "e5-29", source: "electronic_components", target: "wireless_chip" },
  { id: "e5-30", source: "electronic_components", target: "new_display" },
  { id: "e5-31", source: "electronic_components", target: "circuit_board" },
  { id: "e5-32", source: "electronic_components", target: "lithium_battery" },
  { id: "e5-33", source: "electronic_components", target: "discrete_components" },
  { id: "e5-34", source: "electronic_components", target: "power_components" },
  { id: "e5-35", source: "electronic_components", target: "electroacoustic_devices" },
  { id: "e5-36", source: "electronic_components", target: "piezoelectric_devices" },
  { id: "e5-37", source: "electronic_components", target: "other_electronic_components" },

  // 第三层（功能性零部件）到第四层的连接
  { id: "e6-38", source: "functional_components", target: "shell" },
  { id: "e6-39", source: "functional_components", target: "connector" },
  { id: "e6-40", source: "functional_components", target: "structural_parts" },
  { id: "e6-41", source: "functional_components", target: "antenna" },

  // 第三层（模组）到第四层的连接
  { id: "e7-42", source: "modules", target: "fingerprint_module" },
  { id: "e7-43", source: "modules", target: "iris_module" },
  { id: "e7-44", source: "modules", target: "face_module" },
  { id: "e7-45", source: "modules", target: "wireless_module" },
  { id: "e7-46", source: "modules", target: "bluetooth_module" },

  // 第三层（消费类智能终端）到第四层的连接
  { id: "e8-47", source: "consumer_terminals", target: "smartphone" },
  { id: "e8-48", source: "consumer_terminals", target: "tablet" },
  { id: "e8-49", source: "consumer_terminals", target: "laptop" },
  { id: "e8-50", source: "consumer_terminals", target: "wearable_device" },
  { id: "e8-51", source: "consumer_terminals", target: "vehicle_terminal" },
  { id: "e8-52", source: "consumer_terminals", target: "smart_home" },
  { id: "e8-53", source: "consumer_terminals", target: "virtual_display" },
  { id: "e8-54", source: "consumer_terminals", target: "drone" },
  { id: "e8-55", source: "consumer_terminals", target: "smart_robot" },
  { id: "e8-56", source: "consumer_terminals", target: "smart_security" },
  { id: "e8-57", source: "consumer_terminals", target: "smart_instrument" },
  { id: "e8-58", source: "consumer_terminals", target: "financial_terminal" },

  // 第三层（非消费类智能终端）到第四层的连接
  { id: "e9-59", source: "non_consumer_terminals", target: "industrial_software" },
  { id: "e9-60", source: "non_consumer_terminals", target: "support_software" },
  { id: "e9-61", source: "non_consumer_terminals", target: "application_software" },
  { id: "e9-62", source: "non_consumer_terminals", target: "industrial_software_2" },
  { id: "e9-63", source: "non_consumer_terminals", target: "information_security_software" },
  { id: "e9-64", source: "non_consumer_terminals", target: "embedded_software" },
  { id: "e9-65", source: "non_consumer_terminals", target: "other_software" },

  // 第三层（其他智能终端制造）到第四层的连接
  { id: "e10-66", source: "other_terminal_manufacturing", target: "equipment_manufacturer_od" },
  { id: "e10-67", source: "other_terminal_manufacturing", target: "equipment_manufacturer_oe" },

  // 第三层（软件开发服务）到第四层的连接
  { id: "e11-68", source: "software_development", target: "analysis_testing_service" },
  { id: "e11-69", source: "software_development", target: "outsourcing_service" },

  // 第三层（检测认证服务）到第四层的连接
  { id: "e14-70", source: "testing_certification", target: "security_evaluation_service" },
  { id: "e14-71", source: "testing_certification", target: "quality_evaluation_service" },
  { id: "e14-72", source: "testing_certification", target: "product_certification_service" },

  // 第三层（客户管理服务）到第四层的连接
  { id: "e16-73", source: "customer_management", target: "after_sales_service" },
  { id: "e16-74", source: "customer_management", target: "user_experience_service" },
];
