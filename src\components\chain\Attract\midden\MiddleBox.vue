<!--  -->
<template>
  <div style="margin: 0 1.7%" class="flex flex_col flex_J_SB por list_item h32">
    <p class="NB_foot_4 item_title">招商项目清单</p>
    <div class="table_head flex flex_J_SB">
      <div
        class="flex flex_J_C flex_A_C table_headA fs14- font_noWarp text_noWrapA NB_foot_3"
      >
        项目名称
      </div>
      <div
        class="flex flex_J_C flex_A_C table_headB tc fs14- font_noWarp text_noWrapA NB_foot_3"
      >
        县市
      </div>
      <div
        class="flex flex_J_C flex_A_C fs14 table_headB tc por head2 NB_foot_3"
      >
        投资额
      </div>
      <div class="flex flex_J_C flex_A_C fs14 table_headC tc text_l NB_foot_3">
        时间
      </div>
      <div class="flex flex_J_C flex_A_C fs14 table_headC tc text_l NB_foot_3">
        进度
      </div>
    </div>
    <div class="table_body flex1 over">
      <vue3-seamless-scroll
        direction="up"
        :hover="true"
        :isWatch="true"
        :limitScrollNum="right_one_list.length"
        :singleLine="true"
        :list="right_one_list"
        :step="0.4"
        class="DongTable flex flex_col"
      >
        <ul class="flex flex_col flex_J_SB">
          <li
            v-for="(item, i) in right_one_list"
            :key="i"
            class="flex flex_J_SB item_row tr"
            :class="[i % 2 != 1 ? 'row_j' : 'row_t']"
          >
            <div class="flex flex_J_C flex_A_C table_headA">
              <span class="w100 fs14- tc text_noWrapA"> {{ item.A || "暂未填写" }} </span>
            </div>
            <div class="flex flex_A_C fs14- table_headB body_b">
              <span class="w100 tc text_noWrapA">
                {{ item.B || "暂未填写" }}
              </span>
            </div>
            <div class="flex flex_A_C fs14- table_headB body_b">
              <span class="w100 tc text_noWrapA">
                {{ item.C ? item.C + '万元' : "暂未填写" }}
              </span>
            </div>
            <div class="flex flex_J_C flex_A_C fs14 table_headC body_b">
              {{ item.D || "暂未填写" }}
            </div>
            <div class="flex flex_J_C flex_A_C fs14 table_headC body_b">
              建设中
            </div>
          </li>
        </ul>
      </vue3-seamless-scroll>
    </div>
  </div>
</template>

<script setup name="MiddleBox">
import { onMounted, ref } from "vue";
import useCounter from "@/stores/chain/chain.js";
let right_one_list = ref([]);
onMounted(() => {
  const originalData = [
    {
      A: "江西蓝微电子科技有限公司",
      B: "遂川县",
      D: "2021-03",
      E: "4987.5",
    },
    {
      A: "江西红板科技股份有限公司",
      B: "吉州区",
      D: "2022-10",
      E: "65375.36",
    },
    {
      A: "遂川县四联机械有限责任公司",
      B: "安福县",
      D: "2023-06",
      E: "400",
    },
    {
      A: "江西量一光电科技有限公司",
      B: "泰和县",
      D: "2023-12",
      E: "1000",
    },
    {
      A: "宿迁友特照明工程有限公司吉安分公司",
      B: "永丰县",
      D: "2021-09",
      E: "--",
    },
    {
      A: "吉安茂强照明科技有限公司",
      B: "永新县",
      D: "2024-08",
      E: "600",
    },
    {
      A: "江西唯尔佳电子科技有限公司",
      B: "新干县",
      D: "2022-01",
      E: "200",
    },
    {
      A: "吉安市艾吉特道路照明工程有限公司",
      B: "峡江县",
      D: "2024-11",
      E: "2000",
    },
    {
      A: "吉安蓝吉尔蓝宝石有限公司",
      B: "井冈山市",
      D: "2019-08",
      E: "300",
    },
    {
      A: "江西省弘毅服明工程有限公司",
      B: "吉安县",
      D: "2024-11",
      E: "1000",
    },
    {
      A: "江西省志亮照明工程有限公司",
      B: "峡江县",
      D: "2023-04",
      E: "200",
    },
    {
      A: "江西圣烨照明工程有限公司",
      B: "青原区",
      D: "2022-10",
      E: "200",
    },
    {
      A: "吉安市吉森照明工程有限公司",
      B: "泰和县",
      D: "2024-05",
      E: "100",
    },
    {
      A: "吉安市嘉昱实业有限公司",
      B: "新干县",
      D: "2022-11",
      E: "500",
    },
  ];

  // 生成随机投资额并赋值给 item.C
  right_one_list.value = originalData.map(item => {
    const minAmount = 1000; // 1000万元 = 1000万
    const maxAmount = 100000; // 100000万元 = 10亿
    const randomAmount = Math.floor(Math.random() * (maxAmount - minAmount + 1)) + minAmount;
    return {
      ...item,
      C: randomAmount, // 将随机生成的数值赋给 C
    };
  });
});
</script>

<style scoped lang="scss">
.table_head {
  background-color: #015bff;
  height: 3.2vmin;
  min-height: 30px;
  margin-top: 0.5vmin;
}
.head1 {
  padding-left: 10%;
  &::before {
    content: "";
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1.2px;
    height: 60%;
    background-color: #fff;
  }
}

.table_headA {
  width: 20%;
  text-align: left;
}
.table_headC {
  width: 24%;
}
.table_headB {
  width: 30%;
}
.table_body {
  .item_row {
    height: 34px;
    &:hover {
      background-color: rgba(135, 206, 235, 0.2);
    }
  }
  ::v-deep .DongTable {
    & > div {
      display: flex;
      flex-direction: column;
      will-change: transform;
    }
  }
  .row_t {
    color: #718bbd;
  }
  .row_j {
    background: rgba(1, 91, 255, 0.2);
    background: radial-gradient(
      circle,
      rgba(26, 55, 93, 0.6) 40%,
      rgba(14, 29, 61, 0.1) 100%
    );
  }
}
</style>
